import numpy as np
import torch
import time
from FedRec.code.dataset import ClientsDataset, evaluate, evaluate_valid
from FedRec.code.metric import NDCG_binary_at_k_batch, AUC_at_k_batch, HR_at_k_batch
from FedRec.code.untils import getModel


class Clients:
    """
    联邦学习客户端类 - 支持异构设备 (真实通信模拟版)
    改动:
    1. quantize_tensor 被重构为 quantize，仅执行量化操作。
    2. train 方法返回量化后的数据包（张量+元数据），而不是反量化后的浮点数。
    """

    def __init__ ( self, config, logger ):
        self.neg_num = config ['neg_num']
        self.logger = logger
        self.config = config

        # 添加异构设备维度配置
        self.dim_s = config ['dim_s']  # 小型设备嵌入维度
        self.dim_m = config ['dim_m']  # 中型设备嵌入维度
        self.dim_l = config ['dim_l']  # 大型设备嵌入维度

        # 数据路径
        self.data_path = config ['datapath'] + config ['dataset'] + '/' + config ['train_data']
        self.maxlen = config ['max_seq_len']
        self.batch_size = config ['batch_size']

        # 加载客户端数据集
        self.clients_data = ClientsDataset (self.data_path, maxlen = self.maxlen)
        self.dataset = self.clients_data.get_dataset ()
        self.user_train, self.user_valid, self.user_test, self.usernum, self.itemnum = self.dataset

        # 设备选择
        self.device = "cuda" if torch.cuda.is_available () else "cpu"

        # 记录客户端设备类型
        self.device_types = {}  # {uid: 's', 'm', 'l'}
        self._init_device_types ()

        # 创建三种尺寸的模型
        config_s = self.config.copy ()
        config_s ['hidden_size'] = self.dim_s
        self.model_s = getModel (config_s, self.clients_data.get_maxid ()).to (self.device)

        config_m = self.config.copy ()
        config_m ['hidden_size'] = self.dim_m
        self.model_m = getModel (config_m, self.clients_data.get_maxid ()).to (self.device)

        config_l = self.config.copy ()
        config_l ['hidden_size'] = self.dim_l
        self.model_l = getModel (config_l, self.clients_data.get_maxid ()).to (self.device)

        # Server类需要一个统一的model引用，我们让它引用最大模型
        self.model = self.model_l

        self.logger.info (f"异构设备配置: dim_s={self.dim_s}, dim_m={self.dim_m}, dim_l={self.dim_l}")
        self.logger.info (f"设备类型分布: "
                          f"小型:{sum (1 for t in self.device_types.values () if t == 's')}, "
                          f"中型:{sum (1 for t in self.device_types.values () if t == 'm')}, "
                          f"大型:{sum (1 for t in self.device_types.values () if t == 'l')}")

        # 添加量化相关参数
        self.quantize_gradients = config ['quantize_gradients']
        self.quantization_bits = config ['quantization_bits']
        self.quantization_type = config ['quantization_type']

        if self.quantize_gradients:
            self.logger.info (f"启用梯度量化: {self.quantization_bits}位, 类型: {self.quantization_type}")

    def _init_device_types ( self ):
        user_set = self.clients_data.get_user_set ()
        total_users = len (user_set)

        device_split = self.config ['device_split']
        num_s = int (total_users * device_split [0])
        num_m = int (total_users * (device_split [0] + device_split [1]))

        if self.config ['assign_by_interactions']:
            # 获取用户交互次数并排序（升序）
            method = "按交互次序分配"
            user_interactions = {uid: len (self.user_train [uid]) for uid in user_set}
            sorted_users = sorted (user_set, key = lambda uid: user_interactions [uid])
        else:
            # 随机打乱
            method = "随机分配"
            sorted_users = list (user_set)
            np.random.shuffle (sorted_users)

        # 分配设备类型
        for i, uid in enumerate (sorted_users):
            if i < num_s:
                self.device_types [uid] = 's'
            elif i < num_m:
                self.device_types [uid] = 'm'
            else:
                self.device_types [uid] = 'l'

        # 统计结果
        count_s = sum (1 for t in self.device_types.values () if t == 's')
        count_m = sum (1 for t in self.device_types.values () if t == 'm')
        count_l = sum (1 for t in self.device_types.values () if t == 'l')

        self.logger.info (
            f"嵌套设备类型分配完成({method}): "
            f"小型={count_s}({count_s / total_users:.1%}), "
            f"中型={count_m}({count_m / total_users:.1%}), "
            f"大型={count_l}({count_l / total_users:.1%})"
        )

    def load_server_model_params ( self, client_model, server_state_dict ):
        """
        将服务器的全局模型参数加载到指定的客户端模型中。
        """
        client_state_dict = client_model.state_dict ()

        for name, server_param in server_state_dict.items ():
            if name in client_state_dict:
                client_param = client_state_dict [name]
                if server_param.shape != client_param.shape:
                    slicing_indices = [slice (0, dim) for dim in client_param.shape]
                    client_param.copy_ (server_param [tuple (slicing_indices)])
                else:
                    client_param.copy_ (server_param)

    def quantize ( self, tensor, bits, method ):
        """
        仅执行量化操作，返回量化后的张量和反量化所需的元数据。
        """
        if bits == 32 or not self.quantize_gradients:
            return {'quantized': tensor, 'metadata': {}}  # 不量化时，直接返回原张量

        metadata = {}
        if method == 'uniform':
            max_val = torch.max (torch.abs (tensor))
            if max_val == 0:
                # For zero tensor, scale can be 1, no impact.
                return {'quantized': tensor.to (torch.int8), 'metadata': {'scale': 1.0}}
            q_min, q_max = -2 ** (bits - 1), 2 ** (bits - 1) - 1
            scale = max_val / q_max
            quantized = torch.clamp ((tensor / scale).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            return {'quantized': quantized.to (torch.int8), 'metadata': metadata}

        elif method == 'asymmetric':
            min_val, max_val = tensor.min (), tensor.max ()
            if max_val == min_val:
                return {'quantized': tensor.to (torch.uint8), 'metadata': {'scale': 1.0, 'zero_point': 0.0}}

            q_min, q_max = 0, 2 ** bits - 1
            scale = (max_val - min_val) / (q_max - q_min)
            if scale == 0: scale = 1e-8  # Avoid division by zero
            zero_point = q_min - min_val / scale
            quantized = torch.clamp (((tensor / scale) + zero_point).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            metadata ['zero_point'] = zero_point.item ()
            return {'quantized': quantized.to (torch.uint8), 'metadata': metadata}

        # 其他量化方法可以类似地进行拆分...
        # 为保持简洁，此处仅实现了两种最常用方法的拆分

        # 默认回退到不量化
        self.logger.warning (
            f"Quantization method '{method}' not implemented for split operation. Returning full precision tensor.")
        return {'quantized': tensor, 'metadata': {}}

    def sync_models_from_server(self, server):
        """从服务器同步蒸馏后的模型参数"""
        # self.logger.info("同步服务器蒸馏后的模型参数...")

        # 小模型同步
        for param, server_param in zip(self.model_s.parameters(), server.model_s.parameters()):
            param.data.copy_(server_param.data)

        # 中模型同步
        for param, server_param in zip(self.model_m.parameters(), server.model_m.parameters()):
            param.data.copy_(server_param.data)

        # 大模型同步
        for param, server_param in zip(self.model.parameters(), server.model.parameters()):
            param.data.copy_(server_param.data)

    def train ( self, uids, model_param_state_dict, epoch = 0 ):
        self.sync_models_from_server(self)  # 同步模型
        clients_grads = {}
        clients_losses = {}

        for uid in uids:
            uid = uid.item ()
            dev_type = self.device_types.get (uid, 'l')

            if dev_type == 's':
                client_model = self.model_s
            elif dev_type == 'm':
                client_model = self.model_m
            else:
                client_model = self.model_l

            client_model.train ()
            self.load_server_model_params (client_model, model_param_state_dict)

            optimizer = torch.optim.Adam (client_model.parameters (), lr = self.config ['lr'],
                                          betas = (0.9, 0.98), weight_decay = self.config ['l2_reg'])

            # --- 数据准备 ---
            input_seq = self.clients_data.train_seq [uid]
            target_seq = self.clients_data.valid_seq [uid]
            input_len = self.clients_data.seq_len [uid]

            cand = np.setdiff1d (self.clients_data.item_set, self.clients_data.seq [uid])
            prob = self.clients_data.item_prob [cand]
            prob = prob / prob.sum ()
            neg_seq = np.random.choice (cand, (input_len, 100), p = prob)
            neg_seq = np.pad (neg_seq, ((input_seq.shape [0] - input_len, 0), (0, 0)))

            input_seq = torch.from_numpy (input_seq).unsqueeze (0).to (self.device)
            target_seq = torch.from_numpy (target_seq).unsqueeze (0).to (self.device)
            neg_seq = torch.from_numpy (neg_seq).unsqueeze (0).to (self.device)
            input_len = torch.tensor (input_len).unsqueeze (0).to (self.device)

            # --- 训练和梯度计算 ---
            seq_out = client_model(input_seq, input_len)
            padding_mask = (torch.not_equal(input_seq, 0)).float().unsqueeze(-1).to(self.device)

            loss = client_model.loss_function(seq_out, padding_mask, target_seq, neg_seq, input_len)

            clients_losses[uid] = loss.item()


            optimizer.zero_grad ()
            loss.backward ()

            gradients = {name: param.grad.clone () for name, param in client_model.named_parameters () if
                         param.grad is not None}

            # --- 量化梯度并打包 ---
            if self.quantize_gradients:
                quantized_gradients = {}
                for name, grad_tensor in gradients.items ():
                    quantized_gradients [name] = self.quantize (
                        grad_tensor,
                        bits = self.quantization_bits,
                        method = self.quantization_type
                    )
                clients_grads [uid] = quantized_gradients
            else:
                # 如果不量化，也打包成同样的格式，方便服务器统一处理
                clients_grads [uid] = {name: {'quantized': grad, 'metadata': {}} for name, grad in gradients.items ()}

        return clients_grads, clients_losses


class Server:
    """
    联邦学习服务器类 (真实通信模拟版)
    改动:
    1. 新增 dequantize 方法，用于在服务器端恢复梯度。
    2. aggregate_gradients 重写，精确计算上行开销，并先反量化再聚合。
    3. train 方法中的下行开销计算被修正，不再错误地模拟量化。
    """

    def __init__ ( self, config, clients, logger ):
        self.clients = clients
        self.config = config
        self.batch_size = config ['batch_size']
        self.epochs = config ['epochs']
        self.early_stop = config ['early_stop']
        self.maxlen = config ['max_seq_len']
        self.skip_test_eval = config ['skip_test_eval']
        self.eval_freq = config ['eval_freq']
        self.early_stop_enabled = config ['early_stop_enabled']
        self.device = "cuda" if torch.cuda.is_available () else "cpu"
        self.logger = logger
        self.dataset = self.clients.dataset

        # 获取异构设备维度配置
        self.dim_s = config['dim_s']
        self.dim_m = config['dim_m']
        self.dim_l = config['dim_l']

        self.eval_k = config ['eval_k']

        # 初始化模型（使用最大尺寸）
        config ['hidden_size'] = config ['dim_l']
        # 添加知识蒸馏相关参数
        self.kd_ratio = config['kd_ratio']  # 蒸馏物品采样比例
        self.kd_lr = config['kd_lr']  # 蒸馏学习率
        self.distill_epochs = config['distill_epochs']  # 蒸馏轮次

        # 修正1：创建正确的配置对象
        # 创建小模型配置
        config_s = config.copy()
        config_s['hidden_size'] = self.dim_s
        # 创建中模型配置
        config_m = config.copy()
        config_m['hidden_size'] = self.dim_m
        # 创建大模型配置（使用全局模型配置）
        config_l = config.copy()
        config_l['hidden_size'] = self.dim_l

        # 修正2：正确初始化三种尺寸的模型
        self.model_s = getModel(config_s, self.clients.clients_data.get_maxid())
        self.model_m = getModel(config_m, self.clients.clients_data.get_maxid())
        self.model_l = getModel(config_l, self.clients.clients_data.get_maxid())

        # 修正3：将模型转移到设备并初始化参数
        for model in [self.model_s, self.model_m, self.model_l]:
            model.to(self.device)
            # 初始化模型参数
            for name, param in model.named_parameters():
                try:
                    torch.nn.init.xavier_normal_(param.data)
                except:
                    pass  # 忽略初始化失败的层

        # 修正4：确保全局模型与蒸馏大模型一致
        self.model = self.model_l  # 让全局模型引用蒸馏大模型
        self.optimizer = torch.optim.Adam(self.model.parameters(),
                                          lr=config['lr'],
                                          betas=(0.9, 0.98),
                                          weight_decay=config['l2_reg'])

        # 创建蒸馏优化器，包含所有三个模型的参数
        kd_params = list(self.model_s.parameters()) + list(self.model_m.parameters()) + list(self.model_l.parameters())
        self.kd_optimizer = torch.optim.Adam(kd_params, lr=self.kd_lr)

        logger.info("初始化知识蒸馏模块完成")

        logger.info ("服务器初始化完成")
        self._init_comm_cost_calculation ()

    def _knowledge_distillation(self):
        """执行基于关系的集成自知识蒸馏 - 修复尺寸不匹配问题"""
        self.logger.info("开始知识蒸馏步骤...")

        # 1. 准备三个模型
        models = {
            's': self.model_s,
            'm': self.model_m,
            'l': self.model_l
        }

        # 2. 随机选择蒸馏物品子集
        item_set = self.clients.clients_data.item_set
        kd_items = self._select_distill_items(item_set, self.kd_ratio)
        kd_size = len(kd_items)
        self.logger.info(f"蒸馏物品子集大小: {kd_size}个物品")

        # 3. 将物品ID转换为嵌入索引
        # 确保索引在有效范围内
        max_index = self.clients.itemnum - 1
        kd_index = torch.from_numpy(np.clip(kd_items - 1, 0, max_index)).long().to(self.device)

        # 4. 计算每个模型的物品嵌入 - 只针对蒸馏物品子集
        similarity_matrices = {}
        with torch.no_grad():
            for model_type, model in models.items():
                # 只计算蒸馏物品子集的嵌入
                embeddings = model.item_embedding(kd_index)

                # 归一化处理
                norms = embeddings.norm(dim=1, keepdim=True).clamp(min=1e-8)
                norm_emb = embeddings / norms

                # 计算物品间的余弦相似度
                sim_matrix = torch.mm(norm_emb, norm_emb.t())
                similarity_matrices[model_type] = sim_matrix

        # 5. 计算集成相似度 - 确保所有矩阵尺寸一致
        ens_matrix = (similarity_matrices['s'] +
                      similarity_matrices['m'] +
                      similarity_matrices['l']) / 3.0

        # 6. 蒸馏训练
        for epoch_idx in range(self.distill_epochs):
            self.kd_optimizer.zero_grad()

            # 重新计算当前模型的相似度
            current_similarities = {}
            for model_type, model in models.items():
                embeddings = model.item_embedding(kd_index)
                norms = embeddings.norm(dim=1, keepdim=True).clamp(min=1e-8)
                norm_emb = embeddings / norms
                sim_matrix = torch.mm(norm_emb, norm_emb.t())
                current_similarities[model_type] = sim_matrix

            # 计算蒸馏损失 - 使用MSE
            loss = 0
            for model_type in models:
                loss += torch.nn.functional.mse_loss(
                    current_similarities[model_type],
                    ens_matrix.detach()
                )

            # 反向传播
            loss.backward()
            self.kd_optimizer.step()

            self.logger.info(f"蒸馏轮次 [{epoch_idx + 1}/{self.distill_epochs}] 损失: {loss.item():.4f}")

        # 7. 更新客户端模型
        self._update_client_models()

        self.logger.info("知识蒸馏步骤完成")

    def _select_distill_items(self, item_set, ratio):
        """随机选择蒸馏物品子集"""
        num_items = len(item_set)
        kd_size = max(1, int(num_items * ratio))
        return np.random.choice(list(item_set), kd_size, replace=False)

    def _update_client_models(self):
        """将蒸馏后的模型参数同步到客户端"""
        # 小模型更新
        client_s = self.clients.model_s
        for param, server_param in zip(client_s.parameters(), self.model_s.parameters()):
            param.data.copy_(server_param.data)

        # 中模型更新
        client_m = self.clients.model_m
        for param, server_param in zip(client_m.parameters(), self.model_m.parameters()):
            param.data.copy_(server_param.data)

        # 大模型更新（全局模型）
        client_l = self.clients.model
        for param, server_param in zip(client_l.parameters(), self.model_l.parameters()):
            param.data.copy_(server_param.data)

    def _get_param_size ( self, model_or_params ):
        """Calculates the size of model parameters in bytes."""
        if isinstance (model_or_params, torch.nn.Module):
            params = model_or_params.parameters ()
        elif isinstance (model_or_params, dict):
            params = model_or_params.values ()
        else:
            params = model_or_params
        size = 0
        for p in params:
            if isinstance (p, torch.Tensor):
                size += p.nelement () * p.element_size ()
        return size

    def _init_comm_cost_calculation ( self ):
        self.comm_costs = {'downlink': 0, 'uplink': 0}
        self.logger.info ("初始化通信开销计算器...")
        self.size_p_s = self._get_param_size (self.clients.model_s)
        self.size_p_m = self._get_param_size (self.clients.model_m)
        self.size_p_l = self._get_param_size (self.clients.model_l)

        self.logger.info (
            f"模型总尺寸 (MB): S={self.size_p_s / 1e6:.3f}, M={self.size_p_m / 1e6:.3f}, L={self.size_p_l / 1e6:.3f}")

    def dequantize ( self, quant_package, bits, method ):
        """
        在服务器端执行反量化。
        """
        quantized_tensor = quant_package ['quantized']
        metadata = quant_package ['metadata']

        if not metadata:  # 元数据为空，说明未量化
            return quantized_tensor

        if method == 'uniform':
            scale = metadata ['scale']
            return quantized_tensor.float () * scale

        elif method == 'asymmetric':
            scale = metadata ['scale']
            zero_point = metadata ['zero_point']
            return (quantized_tensor.float () - zero_point) * scale

        # 其他方法的反量化逻辑...
        self.logger.info (f"Dequantization for method '{method}' not implemented. Returning quantized tensor.")
        return quantized_tensor.float ()

    def aggregate_gradients ( self, clients_grads ):
        clients_num = len (clients_grads)
        if clients_num == 0:
            self.logger.info ("没有收到任何客户端梯度")
            return

        aggregated_gradients = {name: torch.zeros_like (param) for name, param in self.model.named_parameters () if
                                param.requires_grad}

        # --- 精确计算上行链路开销并执行反量化和聚合 ---
        batch_uplink_cost = 0
        q_bits = self.clients.quantization_bits

        for uid, grads_dict in clients_grads.items ():
            for name, quant_package in grads_dict.items ():
                # 1. 计算通信开销
                if self.clients.quantize_gradients and quant_package ['metadata']:
                    quantized_tensor = quant_package ['quantized']
                    # 计算量化后张量的大小（字节）
                    tensor_cost = quantized_tensor.nelement () * q_bits / 8
                    # 计算元数据的大小（假设每个元数据都是float32=4字节）
                    metadata_cost = len (quant_package ['metadata']) * 4
                    batch_uplink_cost += (tensor_cost + metadata_cost)
                else:  # 未量化
                    batch_uplink_cost += quant_package ['quantized'].nelement () * quant_package [
                        'quantized'].element_size ()

                # 2. 在服务器端反量化
                dequantized_grad = self.dequantize (quant_package, q_bits, self.clients.quantization_type).to (
                    self.device)

                # 3. 聚合反量化后的梯度
                target_shape = aggregated_gradients [name].shape
                if dequantized_grad.shape != target_shape:
                    padded_grad = torch.zeros (target_shape, device = self.device)
                    slicing_indices = tuple (slice (0, dim) for dim in dequantized_grad.shape)
                    padded_grad [slicing_indices] = dequantized_grad
                    aggregated_gradients [name] += padded_grad
                else:
                    aggregated_gradients [name] += dequantized_grad

        self.comm_costs ['uplink'] += batch_uplink_cost

        # --- 应用平均梯度 ---
        for name in aggregated_gradients:
            aggregated_gradients [name] /= clients_num

        for name, param in self.model.named_parameters ():
            if param.requires_grad:
                param.grad = aggregated_gradients.get (name, None)

    def train ( self ):
        self.clients.sync_models_from_server(self)  # 传递服务器实例
        user_set = self.clients.clients_data.get_user_set ()
        uid_seq = [torch.tensor (user_set [i:i + self.batch_size]) for i in range (0, len (user_set), self.batch_size)]

        best_val_ndcg, best_val_hr = 0.0, 0.0
        best_test_ndcg, best_test_hr = 0.0, 0.0
        no_improve_count = 0
        total_time = 0.0
        early_stop_triggered = False
        distill_freq=self.config['distill_freq']
        for epoch in range (self.epochs):
            epoch_start_time = time.time ()
            self.model.train ()

            model_state_to_send = self.model.state_dict ()

            for uids in uid_seq:
                # --- 修复并精确计算下行链路开销 ---
                # 下行链路始终发送完整精度的模型
                downlink_cost_batch = 0
                for uid in uids:
                    dev_type = self.clients.device_types.get (uid.item (), 'l')
                    if dev_type == 's':
                        downlink_cost_batch += self.size_p_s
                    elif dev_type == 'm':
                        downlink_cost_batch += self.size_p_m
                    else:  # 'l'
                        downlink_cost_batch += self.size_p_l
                self.comm_costs ['downlink'] += downlink_cost_batch

                # --- 客户端训练与服务器聚合 ---
                self.optimizer.zero_grad ()
                clients_grads, clients_losses = self.clients.train (uids, model_state_to_send, epoch)
                self.aggregate_gradients (clients_grads)
                self.optimizer.step ()

            epoch_time = time.time () - epoch_start_time
            total_time += epoch_time
            if (epoch + 1) % distill_freq == 0:
                self._knowledge_distillation()
            should_evaluate = (epoch + 1) % self.eval_freq == 0 or epoch == 0 or epoch == self.epochs - 1
            if should_evaluate:
                self.model.eval ()
                t1 = time.time () - epoch_start_time
                t_valid = evaluate_valid (self.model, self.dataset, self.maxlen, self.clients.neg_num, self.eval_k,
                                          self.config ['full_eval'], self.device)

                # --- 通信开销计算: 日志记录 ---
                down_mb = self.comm_costs ['downlink'] / (1024 ** 2)
                up_mb = self.comm_costs ['uplink'] / (1024 ** 2)
                total_mb = down_mb + up_mb
                self.logger.info (f"COMMUNICATION COST - Epoch {epoch + 1}: "
                                 f"Downlink={down_mb:.3f}MB, Uplink={up_mb:.3f}MB, Total={total_mb:.3f}MB")
                # ---

                self.logger.info (
                    f"传统评估结果 - Epoch {epoch + 1}: NDCG@{self.eval_k}={t_valid[0]:.4f}, HR@{self.eval_k}={t_valid[1]:.4f}")

                # 检查评估结果是否异常
                if t_valid[0] > 1.0 or t_valid[1] > 1.0 or np.isnan(t_valid[0]) or np.isnan(t_valid[1]):
                    self.logger.info (
                        f"检测到异常评估结果: NDCG@{self.eval_k}={t_valid[0]:.4f}, HR@{self.eval_k}={t_valid[1]:.4f}")

                # 早停检查
                if self.early_stop_enabled:
                    if t_valid [0] > best_val_ndcg:
                        # NDCG有改善，重置计数器
                        no_improve_count = 0
                        best_val_ndcg = t_valid [0]
                    else:
                        # NDCG没有改善，增加计数器
                        no_improve_count += 1

                    # 如果连续多轮没有改善，触发早停
                    if no_improve_count >= self.early_stop:
                        self.logger.info (f"早停触发！NDCG在{self.early_stop}轮内没有改善。")
                        early_stop_triggered = True

                # 测试集评估（可选）
                if not self.skip_test_eval:
                    t_test = evaluate (self.model, self.dataset, self.maxlen, self.clients.neg_num, self.eval_k,
                                       self.config ['full_eval'], self.device)

                    # 检查测试集评估结果是否异常
                    if t_test[0] > 1.0 or t_test[1] > 1.0 or np.isnan(t_test[0]) or np.isnan(t_test[1]):
                        self.logger.info (
                            f"检测到异常测试结果: NDCG@{self.eval_k}={t_test[0]:.4f}, HR@{self.eval_k}={t_test[1]:.4f}")

                    self.logger.info (
                        'epoch:%d, time: %f(s), valid (NDCG@%d: %.4f, HR@%d: %.4f), test (NDCG@%d: %.4f, HR@%d: %.4f), all_time: %f(s)'
                        % (epoch + 1, t1, self.eval_k, t_valid [0], self.eval_k, t_valid [1], self.eval_k, t_test [0],
                           self.eval_k, t_test [1], total_time))
                else:
                    self.logger.info (
                        'epoch:%d, time: %f(s), valid (NDCG@%d: %.4f, HR@%d: %.4f), test: SKIPPED, all_time: %f(s)'
                        % (epoch + 1, t1, self.eval_k, t_valid [0], self.eval_k, t_valid [1], total_time))

                # 更新最佳结果
                if not self.skip_test_eval:
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr or t_test[0] > best_test_ndcg or \
                            t_test[1] > best_test_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        best_test_ndcg = max(t_test[0], best_test_ndcg)
                        best_test_hr = max(t_test[1], best_test_hr)
                        self.logger.info (
                            f"新的最佳性能: valid NDCG@{self.eval_k}={best_val_ndcg:.4f}, test NDCG@{self.eval_k}={best_test_ndcg:.4f}")

                else:
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        self.logger.info (
                            f"新的最佳性能: valid NDCG@{self.eval_k}={best_val_ndcg:.4f}, valid HR@{self.eval_k}={best_val_hr:.4f}")

                self.model.train()

            if early_stop_triggered:
                break

        # 记录最佳结果
        if not self.skip_test_eval:
            self.logger.info (
                '[联邦训练] 最佳结果: valid NDCG@{}={:.4f}, HR@{}={:.4f}, test NDCG@{}={:.4f}, HR@{}={:.4f}'.format (
                    self.eval_k, best_val_ndcg, self.eval_k, best_val_hr, self.eval_k, best_test_ndcg, self.eval_k,
                    best_test_hr))
        else:
            self.logger.info ('[联邦训练] 最佳结果: valid NDCG@{}={:.4f}, HR@{}={:.4f} (测试集评估已跳过)'.format (
                self.eval_k, best_val_ndcg, self.eval_k, best_val_hr))
