import numpy as np
import torch
import time
from FedRec.code.dataset import ClientsDataset, evaluate, evaluate_valid
from FedRec.code.metric import NDCG_binary_at_k_batch, AUC_at_k_batch, HR_at_k_batch
from FedRec.code.untils import getModel


class Clients:
    """
    联邦学习客户端类 - 支持异构设备 (真实通信模拟版)
    改动:
    1. 新增 dequantize_and_load_model 方法，用于处理服务器下发的量化模型。
    2. train 方法的输入参数变为量化后的模型包。
    """

    def __init__ ( self, config, logger ):
        self.neg_num = config ['neg_num']
        self.logger = logger
        self.config = config

        # 添加异构设备维度配置
        self.dim_s = config ['dim_s']
        self.dim_m = config ['dim_m']
        self.dim_l = config ['dim_l']

        # 数据路径
        self.data_path = config ['datapath'] + config ['dataset'] + '/' + config ['train_data']
        self.maxlen = config ['max_seq_len']
        self.batch_size = config ['batch_size']

        # 加载客户端数据集
        self.clients_data = ClientsDataset (self.data_path, maxlen = self.maxlen)
        self.dataset = self.clients_data.get_dataset ()
        self.user_train, self.user_valid, self.user_test, self.usernum, self.itemnum = self.dataset

        # 设备选择
        self.device = "cuda" if torch.cuda.is_available () else "cpu"

        # 记录客户端设备类型
        self.device_types = {}
        self._init_device_types ()

        # 创建三种尺寸的模型
        config_s = self.config.copy ()
        config_s ['hidden_size'] = self.dim_s
        self.model_s = getModel (config_s, self.clients_data.get_maxid ()).to (self.device)

        config_m = self.config.copy ()
        config_m ['hidden_size'] = self.dim_m
        self.model_m = getModel (config_m, self.clients_data.get_maxid ()).to (self.device)

        config_l = self.config.copy ()
        config_l ['hidden_size'] = self.dim_l
        self.model_l = getModel (config_l, self.clients_data.get_maxid ()).to (self.device)

        self.model = self.model_l

        self.logger.info (f"异构设备配置: dim_s={self.dim_s}, dim_m={self.dim_m}, dim_l={self.dim_l}")
        self.logger.info (f"设备类型分布: "
                          f"小型:{sum (1 for t in self.device_types.values () if t == 's')}, "
                          f"中型:{sum (1 for t in self.device_types.values () if t == 'm')}, "
                          f"大型:{sum (1 for t in self.device_types.values () if t == 'l')}")

        self.quantize_gradients = config ['quantize_gradients']
        self.quantization_bits = config ['quantization_bits']
        self.quantization_type = config ['quantization_type']

        if self.quantize_gradients:
            self.logger.info (f"启用梯度量化 (双向): {self.quantization_bits}位, 类型: {self.quantization_type}")

    def _init_device_types ( self ):
        user_set = self.clients_data.get_user_set ()
        total_users = len (user_set)
        device_split = self.config ['device_split']
        num_s = int (total_users * device_split [0])
        num_m = int (total_users * (device_split [0] + device_split [1]))

        if self.config ['assign_by_interactions']:
            method = "按交互次序分配"
            user_interactions = {uid: len (self.user_train [uid]) for uid in user_set}
            sorted_users = sorted (user_set, key = lambda uid: user_interactions [uid])
        else:
            method = "随机分配"
            sorted_users = list (user_set)
            np.random.shuffle (sorted_users)

        for i, uid in enumerate (sorted_users):
            if i < num_s:
                self.device_types [uid] = 's'
            elif i < num_m:
                self.device_types [uid] = 'm'
            else:
                self.device_types [uid] = 'l'

        count_s = sum (1 for t in self.device_types.values () if t == 's')
        count_m = sum (1 for t in self.device_types.values () if t == 'm')
        count_l = sum (1 for t in self.device_types.values () if t == 'l')

        self.logger.info (
            f"嵌套设备类型分配完成({method}): "
            f"小型={count_s}({count_s / total_users:.1%}), "
            f"中型={count_m}({count_m / total_users:.1%}), "
            f"大型={count_l}({count_l / total_users:.1%})"
        )

    # +++ NEW +++
    def dequantize_tensor ( self, quant_package, bits, method ):
        """
        在客户端端执行张量的反量化。
        """
        quantized_tensor = quant_package ['quantized']
        metadata = quant_package ['metadata']

        if not metadata:  # 元数据为空，说明未量化
            return quantized_tensor

        if method == 'uniform':
            scale = metadata ['scale']
            return quantized_tensor.float () * scale

        elif method == 'asymmetric':
            scale = metadata ['scale']
            zero_point = metadata ['zero_point']
            return (quantized_tensor.float () - zero_point) * scale

        return quantized_tensor.float ()

    # +++ NEW +++
    def dequantize_and_load_model ( self, client_model, quant_model_package ):
        """
        反量化服务器模型并加载到本地。
        """
        client_state_dict = client_model.state_dict ()

        for name, quant_package in quant_model_package.items ():
            if name in client_state_dict:
                # 1. 反量化
                dequantized_param = self.dequantize_tensor (
                    quant_package,
                    self.quantization_bits,
                    self.quantization_type
                ).to (self.device)

                # 2. 加载（含切片）
                client_param = client_state_dict [name]
                if dequantized_param.shape != client_param.shape:
                    slicing_indices = [slice (0, dim) for dim in client_param.shape]
                    client_param.copy_ (dequantized_param [tuple (slicing_indices)])
                else:
                    client_param.copy_ (dequantized_param)

    def quantize ( self, tensor, bits, method ):
        if bits == 32 or not self.quantize_gradients:
            return {'quantized': tensor, 'metadata': {}}

        metadata = {}
        if method == 'uniform':
            max_val = torch.max (torch.abs (tensor))
            if max_val == 0:
                return {'quantized': tensor.to (torch.int8), 'metadata': {'scale': 1.0}}
            q_min, q_max = -2 ** (bits - 1), 2 ** (bits - 1) - 1
            scale = max_val / q_max
            quantized = torch.clamp ((tensor / scale).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            # --- MODIFIED --- 使用符合比特数的类型
            dtype = torch.int8 if bits <= 8 else torch.int16 if bits <= 16 else torch.int32
            return {'quantized': quantized.to (dtype), 'metadata': metadata}

        elif method == 'asymmetric':
            min_val, max_val = tensor.min (), tensor.max ()
            if max_val == min_val:
                return {'quantized': tensor.to (torch.uint8), 'metadata': {'scale': 1.0, 'zero_point': 0.0}}

            q_min, q_max = 0, 2 ** bits - 1
            scale = (max_val - min_val) / (q_max - q_min)
            if scale == 0: scale = 1e-8
            zero_point = q_min - min_val / scale
            quantized = torch.clamp (((tensor / scale) + zero_point).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            metadata ['zero_point'] = zero_point.item ()
            # --- MODIFIED --- 使用符合比特数的类型
            dtype = torch.uint8 if bits <= 8 else torch.uint16 if bits <= 16 else torch.uint32
            return {'quantized': quantized.to (dtype), 'metadata': metadata}

        self.logger.warning (f"Quantization method '{method}' not fully implemented. Returning full precision tensor.")
        return {'quantized': tensor, 'metadata': {}}

    # --- MODIFIED --- 输入参数变为 quant_model_package
    def train ( self, uids, quant_model_package, epoch = 0 ):
        clients_grads = {}
        clients_losses = {}

        for uid in uids:
            uid = uid.item ()
            dev_type = self.device_types.get (uid, 'l')

            if dev_type == 's':
                client_model = self.model_s
            elif dev_type == 'm':
                client_model = self.model_m
            else:
                client_model = self.model_l

            client_model.train ()

            # --- MODIFIED --- 第一步：反量化并加载服务器下发的模型
            if self.quantize_gradients:
                self.dequantize_and_load_model (client_model, quant_model_package)
            else:  # 如果不启用量化，包内就是原始张量
                dequantized_dict = {name: pkg ['quantized'] for name, pkg in quant_model_package.items ()}
                client_model.load_state_dict (dequantized_dict, strict = False)

            optimizer = torch.optim.Adam (client_model.parameters (), lr = self.config ['lr'],
                                          betas = (0.9, 0.98), weight_decay = self.config ['l2_reg'])

            # 数据准备
            input_seq = self.clients_data.train_seq [uid]
            target_seq = self.clients_data.valid_seq [uid]
            input_len = self.clients_data.seq_len [uid]
            cand = np.setdiff1d (self.clients_data.item_set, self.clients_data.seq [uid])
            prob = self.clients_data.item_prob [cand]
            prob = prob / prob.sum ()
            neg_seq = np.random.choice (cand, (input_len, 100), p = prob)
            neg_seq = np.pad (neg_seq, ((input_seq.shape [0] - input_len, 0), (0, 0)))
            input_seq = torch.from_numpy (input_seq).unsqueeze (0).to (self.device)
            target_seq = torch.from_numpy (target_seq).unsqueeze (0).to (self.device)
            neg_seq = torch.from_numpy (neg_seq).unsqueeze (0).to (self.device)
            input_len = torch.tensor (input_len).unsqueeze (0).to (self.device)

            # 训练和梯度计算
            seq_out = client_model (input_seq, input_len)
            padding_mask = (torch.not_equal (input_seq, 0)).float ().unsqueeze (-1).to (self.device)
            loss = client_model.loss_function (seq_out, padding_mask, target_seq, neg_seq, input_len)
            clients_losses [uid] = loss.item ()

            optimizer.zero_grad ()
            loss.backward ()

            gradients = {name: param.grad.clone () for name, param in client_model.named_parameters () if
                         param.grad is not None}

            # 量化梯度并打包
            if self.quantize_gradients:
                quantized_gradients = {}
                for name, grad_tensor in gradients.items ():
                    quantized_gradients [name] = self.quantize (
                        grad_tensor,
                        bits = self.quantization_bits,
                        method = self.quantization_type
                    )
                clients_grads [uid] = quantized_gradients
            else:
                clients_grads [uid] = {name: {'quantized': grad, 'metadata': {}} for name, grad in gradients.items ()}

        return clients_grads, clients_losses


class Server:
    """
    联邦学习服务器类 (真实通信模拟版)
    改动:
    1. 新增 quantize_model 方法，用于下行链路的模型量化。
    2. aggregate_gradients 和 train 中的开销计算改为直接计算数据包大小。
    """

    def __init__ ( self, config, clients, logger ):
        self.clients = clients
        self.config = config
        self.batch_size = config ['batch_size']
        self.epochs = config ['epochs']
        self.early_stop = config ['early_stop']
        self.maxlen = config ['max_seq_len']
        self.skip_test_eval = config ['skip_test_eval']
        self.eval_freq = config ['eval_freq']
        self.early_stop_enabled = config ['early_stop_enabled']
        self.device = "cuda" if torch.cuda.is_available () else "cpu"
        self.logger = logger
        self.dataset = self.clients.dataset

        # 获取异构设备维度配置
        self.dim_s = config ['dim_s']
        self.dim_m = config ['dim_m']
        self.dim_l = config ['dim_l']

        self.eval_k = config ['eval_k']

        config ['hidden_size'] = config ['dim_l']
        self.model = getModel (config, self.clients.clients_data.get_maxid ())
        self.model.to (self.device)

        self.optimizer = torch.optim.Adam (self.model.parameters (), lr = config ['lr'],
                                           betas = (0.9, 0.98), weight_decay = config ['l2_reg'])
        logger.info ("服务器初始化完成")
        self._init_comm_cost_calculation ()

    def _get_param_size ( self, model_or_params ):
        if isinstance (model_or_params, torch.nn.Module):
            params = model_or_params.parameters ()
        elif isinstance (model_or_params, dict):
            params = model_or_params.values ()
        else:
            params = model_or_params
        size = 0
        for p in params:
            if isinstance (p, torch.Tensor):
                size += p.nelement () * p.element_size ()
        return size

    # +++ NEW +++
    def _get_quant_package_size ( self, quant_package ):
        """直接计算一个量化包的实际字节大小"""
        size = 0
        for name, package in quant_package.items ():
            # 计算量化后张量的大小
            tensor = package ['quantized']
            size += tensor.nelement () * tensor.element_size ()
            # 计算元数据的大小（假设每个元数据都是float32=4字节）
            size += len (package ['metadata']) * 4
        return size

    def _init_comm_cost_calculation ( self ):
        self.comm_costs = {'downlink': 0, 'uplink': 0}
        self.logger.info ("初始化通信开销计算器...")
        self.size_p_s = self._get_param_size (self.clients.model_s)
        self.size_p_m = self._get_param_size (self.clients.model_m)
        self.size_p_l = self._get_param_size (self.clients.model_l)
        self.logger.info (
            f"模型总尺寸 (MB): S={self.size_p_s / 1e6:.3f}, M={self.size_p_m / 1e6:.3f}, L={self.size_p_l / 1e6:.3f}")

    # +++ NEW +++ (逻辑基本同客户端)
    def quantize_tensor ( self, tensor, bits, method ):
        if bits == 32 or not self.clients.quantize_gradients:
            return {'quantized': tensor, 'metadata': {}}

        metadata = {}
        if method == 'uniform':
            max_val = torch.max (torch.abs (tensor))
            if max_val == 0:
                return {'quantized': tensor.to (torch.int8), 'metadata': {'scale': 1.0}}
            q_min, q_max = -2 ** (bits - 1), 2 ** (bits - 1) - 1
            scale = max_val / q_max
            quantized = torch.clamp ((tensor / scale).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            dtype = torch.int8 if bits <= 8 else torch.int16 if bits <= 16 else torch.int32
            return {'quantized': quantized.to (dtype), 'metadata': metadata}

        elif method == 'asymmetric':
            min_val, max_val = tensor.min (), tensor.max ()
            if max_val == min_val:
                return {'quantized': tensor.to (torch.uint8), 'metadata': {'scale': 1.0, 'zero_point': 0.0}}
            q_min, q_max = 0, 2 ** bits - 1
            scale = (max_val - min_val) / (q_max - q_min)
            if scale == 0: scale = 1e-8
            zero_point = q_min - min_val / scale
            quantized = torch.clamp (((tensor / scale) + zero_point).round (), q_min, q_max)
            metadata ['scale'] = scale.item ()
            metadata ['zero_point'] = zero_point.item ()
            dtype = torch.uint8 if bits <= 8 else torch.uint16 if bits <= 16 else torch.uint32
            return {'quantized': quantized.to (dtype), 'metadata': metadata}

        self.logger.warning (f"Quantization method '{method}' not fully implemented. Returning full precision tensor.")
        return {'quantized': tensor, 'metadata': {}}

    def dequantize ( self, quant_package, bits, method ):
        quantized_tensor = quant_package ['quantized']
        metadata = quant_package ['metadata']

        if not metadata:
            return quantized_tensor

        if method == 'uniform':
            scale = metadata ['scale']
            return quantized_tensor.float () * scale

        elif method == 'asymmetric':
            scale = metadata ['scale']
            zero_point = metadata ['zero_point']
            return (quantized_tensor.float () - zero_point) * scale

        self.logger.warning (f"Dequantization for method '{method}' not implemented. Returning quantized tensor.")
        return quantized_tensor.float ()

    def aggregate_gradients ( self, clients_grads ):
        clients_num = len (clients_grads)
        if clients_num == 0:
            self.logger.info ("没有收到任何客户端梯度")
            return

        aggregated_gradients = {name: torch.zeros_like (param) for name, param in self.model.named_parameters () if
                                param.requires_grad}

        # --- MODIFIED --- 直接在处理接收数据时计算上行开销
        batch_uplink_cost = 0
        q_bits = self.clients.quantization_bits

        for uid, grads_dict in clients_grads.items ():
            # 1. 计算该客户端上传的实际通信开销
            batch_uplink_cost += self._get_quant_package_size (grads_dict)

            for name, quant_package in grads_dict.items ():
                # 2. 在服务器端反量化
                dequantized_grad = self.dequantize (quant_package, q_bits, self.clients.quantization_type).to (
                    self.device)

                # 3. 聚合反量化后的梯度
                target_shape = aggregated_gradients [name].shape
                if dequantized_grad.shape != target_shape:
                    padded_grad = torch.zeros (target_shape, device = self.device)
                    slicing_indices = tuple (slice (0, dim) for dim in dequantized_grad.shape)
                    padded_grad [slicing_indices] = dequantized_grad
                    aggregated_gradients [name] += padded_grad
                else:
                    aggregated_gradients [name] += dequantized_grad

        self.comm_costs ['uplink'] += batch_uplink_cost

        # 应用平均梯度
        for name in aggregated_gradients:
            aggregated_gradients [name] /= clients_num

        for name, param in self.model.named_parameters ():
            if param.requires_grad:
                param.grad = aggregated_gradients.get (name, None)

    def train ( self ):
        user_set = self.clients.clients_data.get_user_set ()
        uid_seq = [torch.tensor (user_set [i:i + self.batch_size]) for i in range (0, len (user_set), self.batch_size)]

        best_val_ndcg, best_val_hr = 0.0, 0.0
        best_test_ndcg, best_test_hr = 0.0, 0.0
        no_improve_count = 0
        total_time = 0.0
        early_stop_triggered = False

        for epoch in range (self.epochs):
            epoch_start_time = time.time ()
            self.model.train ()

            # --- MODIFIED --- 根据是否启用量化，准备要发送的模型包
            if self.clients.quantize_gradients:
                # 对完整模型进行一次量化
                model_to_send = {
                    name: self.quantize_tensor (param, self.clients.quantization_bits, self.clients.quantization_type)
                    for name, param in self.model.state_dict ().items ()
                }
            else:
                # 否则，打包成非量化格式
                model_to_send = {
                    name: {'quantized': param, 'metadata': {}}
                    for name, param in self.model.state_dict ().items ()
                }

            # --- MODIFIED --- 直接计算下行开销
            downlink_cost_s = self._get_quant_package_size ({
                name: pkg for name, pkg in model_to_send.items () if name in self.clients.model_s.state_dict ()
            })
            downlink_cost_m = self._get_quant_package_size ({
                name: pkg for name, pkg in model_to_send.items () if name in self.clients.model_m.state_dict ()
            })
            downlink_cost_l = self._get_quant_package_size (model_to_send)

            for uids in uid_seq:
                # --- MODIFIED --- 累加本批次精确的下行开销
                downlink_cost_batch = 0
                for uid in uids:
                    dev_type = self.clients.device_types.get (uid.item (), 'l')
                    if dev_type == 's':
                        downlink_cost_batch += downlink_cost_s
                    elif dev_type == 'm':
                        downlink_cost_batch += downlink_cost_m
                    else:  # 'l'
                        downlink_cost_batch += downlink_cost_l
                self.comm_costs ['downlink'] += downlink_cost_batch

                # 客户端训练与服务器聚合
                self.optimizer.zero_grad ()
                # --- MODIFIED --- 发送准备好的模型包
                clients_grads, clients_losses = self.clients.train (uids, model_to_send, epoch)
                self.aggregate_gradients (clients_grads)
                self.optimizer.step ()

            epoch_time = time.time () - epoch_start_time
            total_time += epoch_time

            should_evaluate = (epoch + 1) % self.eval_freq == 0 or epoch == 0 or epoch == self.epochs - 1
            if should_evaluate:
                self.model.eval ()
                t1 = time.time () - epoch_start_time
                t_valid = evaluate_valid (self.model, self.dataset, self.maxlen, self.clients.neg_num, self.eval_k,
                                          self.config ['full_eval'], self.device)

                # --- 通信开销计算: 日志记录 ---
                down_mb = self.comm_costs ['downlink'] / (1024 ** 2)
                up_mb = self.comm_costs ['uplink'] / (1024 ** 2)
                total_mb = down_mb + up_mb
                self.logger.info (f"COMMUNICATION COST - Epoch {epoch + 1}: "
                                  f"Downlink={down_mb:.3f}MB, Uplink={up_mb:.3f}MB, Total={total_mb:.3f}MB")
                # ---

                self.logger.info (
                    f"传统评估结果 - Epoch {epoch + 1}: NDCG@{self.eval_k}={t_valid [0]:.4f}, HR@{self.eval_k}={t_valid [1]:.4f}")

                # 检查评估结果是否异常
                if t_valid [0] > 1.0 or t_valid [1] > 1.0 or np.isnan (t_valid [0]) or np.isnan (t_valid [1]):
                    self.logger.info (
                        f"检测到异常评估结果: NDCG@{self.eval_k}={t_valid [0]:.4f}, HR@{self.eval_k}={t_valid [1]:.4f}")

                # 早停检查
                if self.early_stop_enabled:
                    if t_valid [0] > best_val_ndcg:
                        # NDCG有改善，重置计数器
                        no_improve_count = 0
                        best_val_ndcg = t_valid [0]
                    else:
                        # NDCG没有改善，增加计数器
                        no_improve_count += 1

                    # 如果连续多轮没有改善，触发早停
                    if no_improve_count >= self.early_stop:
                        self.logger.info (f"早停触发！NDCG在{self.early_stop}轮内没有改善。")
                        early_stop_triggered = True

                # 测试集评估（可选）
                if not self.skip_test_eval:
                    t_test = evaluate (self.model, self.dataset, self.maxlen, self.clients.neg_num, self.eval_k,
                                       self.config ['full_eval'], self.device)

                    # 检查测试集评估结果是否异常
                    if t_test [0] > 1.0 or t_test [1] > 1.0 or np.isnan (t_test [0]) or np.isnan (t_test [1]):
                        self.logger.info (
                            f"检测到异常测试结果: NDCG@{self.eval_k}={t_test [0]:.4f}, HR@{self.eval_k}={t_test [1]:.4f}")

                    self.logger.info (
                        'epoch:%d, time: %f(s), valid (NDCG@%d: %.4f, HR@%d: %.4f), test (NDCG@%d: %.4f, HR@%d: %.4f), all_time: %f(s)'
                        % (epoch + 1, t1, self.eval_k, t_valid [0], self.eval_k, t_valid [1], self.eval_k, t_test [0],
                           self.eval_k, t_test [1], total_time))
                else:
                    self.logger.info (
                        'epoch:%d, time: %f(s), valid (NDCG@%d: %.4f, HR@%d: %.4f), test: SKIPPED, all_time: %f(s)'
                        % (epoch + 1, t1, self.eval_k, t_valid [0], self.eval_k, t_valid [1], total_time))

                # 更新最佳结果
                if not self.skip_test_eval:
                    if t_valid [0] > best_val_ndcg or t_valid [1] > best_val_hr or t_test [0] > best_test_ndcg or \
                            t_test [1] > best_test_hr:
                        best_val_ndcg = max (t_valid [0], best_val_ndcg)
                        best_val_hr = max (t_valid [1], best_val_hr)
                        best_test_ndcg = max (t_test [0], best_test_ndcg)
                        best_test_hr = max (t_test [1], best_test_hr)
                        self.logger.info (
                            f"新的最佳性能: valid NDCG@{self.eval_k}={best_val_ndcg:.4f}, test NDCG@{self.eval_k}={best_test_ndcg:.4f}")

                else:
                    if t_valid [0] > best_val_ndcg or t_valid [1] > best_val_hr:
                        best_val_ndcg = max (t_valid [0], best_val_ndcg)
                        best_val_hr = max (t_valid [1], best_val_hr)
                        self.logger.info (
                            f"新的最佳性能: valid NDCG@{self.eval_k}={best_val_ndcg:.4f}, valid HR@{self.eval_k}={best_val_hr:.4f}")

                self.model.train ()

            if early_stop_triggered:
                break

                # 记录最佳结果
            if not self.skip_test_eval:
                self.logger.info (
                    '[联邦训练] 最佳结果: valid NDCG@{}={:.4f}, HR@{}={:.4f}, test NDCG@{}={:.4f}, HR@{}={:.4f}'.format (
                        self.eval_k, best_val_ndcg, self.eval_k, best_val_hr, self.eval_k, best_test_ndcg, self.eval_k,
                        best_test_hr))
            else:
                self.logger.info ('[联邦训练] 最佳结果: valid NDCG@{}={:.4f}, HR@{}={:.4f} (测试集评估已跳过)'.format (
                    self.eval_k, best_val_ndcg, self.eval_k, best_val_hr))
