# -*- coding: utf-8 -*-
from parse import config
from untils import Logger

if __name__ == '__main__':
    algorithm=config['algorithm']


    # 创建日志记录器
    logger = Logger(config)
    logger.info(f"算法: {algorithm}")
    logger.info("开始训练，配置参数如下：")
    for key, value in config.items():
        logger.info(f"{key}: {value}")

    # 打印数据集路径信息
    train_data_path = config['datapath'] + config['dataset'] + '/' + config['train_data']
    logger.info(f"训练数据: {train_data_path}")


    logger.info(f"最大序列长度: {config['max_seq_len']}")
    logger.info(f"批次大小: {config['batch_size']}")

    # 根据 algorithm 动态导入模块
    if algorithm == 'base_Q':
        from Fed_base_Q import Clients, Server
    elif algorithm == 'base_Top_k':
        from Fed_base_Top_k import Clients, Server
    elif algorithm == 'base_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_base_DHC import Clients, Server
    elif algorithm == 'UDL_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_UDL_DHC import Clients, Server
    elif algorithm == 'UDL_DDR_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_UDL_DDR_DHC import Clients, Server
    elif algorithm == 'UDL_DDR_RESKD_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_UDL_DDR_RESKD_DHC import Clients, Server
    elif algorithm == 'DDR_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_DDR_DHC import Clients, Server
    elif algorithm == 'RESKD_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_RESKD_DHC import Clients, Server
    elif algorithm == 'DDR_RESKD_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_DDR_RESKD_DHC import Clients, Server
    elif algorithm == 'RESKD_I_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_RESKD_DHC import Clients, Server
    elif algorithm == 'UDL_RESKD_DHC':
        from FedRec.code.DivisiveHierarchicalClustering.Fed_UDL_RESKD_DHC import Clients, Server
    else:
        raise ValueError(f"不支持的算法: {algorithm}")
    # 构建客户端
    clients = Clients(config, logger)
    logger.info(f"用户数量: {clients.usernum}")
    logger.info(f"物品数量: {clients.itemnum}")

    # 构建服务器
    server = Server(config, clients, logger)

    # 开始训练
    server.train()

