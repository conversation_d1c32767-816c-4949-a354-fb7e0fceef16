import torch
import torch.nn.functional as F

class DivisiveHierarchicalClustering:
    """
    实现一个自顶向下或称为"分裂"的层次聚类算法。
    """

    def __init__(self, config, original_data, encoded_data, cluster_queue):
        self.config = config
        self.original_data = original_data
        self.encoded_data = encoded_data
        self.cluster_queue = cluster_queue
        # 计算初始簇的"离差"或紧密度。
        self._get_deviation()

        # 添加最大迭代次数限制
        self.max_iterations = config.get('max_iterations', 1000)
        self.iteration_count = 0

    def _get_deviation(self):
        """
        为 cluster_queue 中的每个簇计算其紧密度得分。
        """
        self.deviation = []
        for cluster in self.cluster_queue:
            # 如果一个簇只有一个成员，那么它的内聚性是完美的（得分为1.0）。
            if len(cluster) == 1:
                self.deviation.append(1.0)
            else:
                # 获取当前簇中所有成员的原始梯度向量和它们对应的质心向量。
                cluster_original_data = self.original_data[cluster]
                cluster_encoded_data = self.encoded_data[cluster]
                # 计算每个数据点与其所属簇质心之间的平均余弦相似度。
                self.deviation.append(
                    torch.mean(F.cosine_similarity(cluster_original_data, cluster_encoded_data, dim=1)))
        # 将得分列表转换为张量，以便高效地执行操作（如 argmin）。
        self.deviation = torch.tensor(self.deviation)

    def _split_cluster(self, cluster_item, cluster_original_data):
        """
        将一个单独的簇分裂成两个子簇。
        """
        # 如果簇中只有一个点，无法分裂，直接返回
        if len(cluster_item) <= 1:
            return cluster_item, torch.tensor([], dtype=torch.long)  # 返回原簇和一个空簇

        # 确保cluster_original_data是二维张量
        if cluster_original_data.dim() != 2:
            raise ValueError(f"cluster_original_data 必须是二维张量，但得到 {cluster_original_data.dim()} 维")

        n, d = cluster_original_data.shape
        if n == 0:
            raise ValueError("cluster_original_data 不能为空")

        # 添加额外检查：确保n>1
        if n <= 1:
            return cluster_item, torch.tensor([], dtype=torch.long)

        # 将向量归一化为单位长度，这样它们的点积就等于余弦相似度。
        norm_cluster_original_data = F.normalize(cluster_original_data, p=2, dim=1)  # 形状: [n, d]
        # 计算簇内所有成员之间的成对余弦相似度矩阵。
        similarity_matrix = norm_cluster_original_data @ norm_cluster_original_data.T  # 形状: [n, n]

        # 为了找到相似度最低的一对，我们需要忽略自身与自身的相似度（即对角线上的值），
        # 方法是将其设置为一个非常大的数。
        similarity_matrix.fill_diagonal_(9999.0)

        # 在扁平化的相似度矩阵中找到最小值的索引。
        # 这对应于簇中两个最不相似的成员。
        min_idx = similarity_matrix.argmin()
        i = min_idx // n  # 第一个种子成员的行索引。
        j = min_idx % n  # 第二个种子成员的列索引。

        # 划分簇。每个成员根据它与种子i和种子j的相似度大小，被分配到更相似的那个种子所在的子簇。
        cluster_i = cluster_item[similarity_matrix[i] >= similarity_matrix[j]]
        cluster_j = cluster_item[similarity_matrix[i] < similarity_matrix[j]]

        # 更新新形成的两个子簇的 encoded_data (质心)。
        if len(cluster_i) == 1:
            self.encoded_data[cluster_i] = self.original_data[cluster_i]
        else:
            self.encoded_data[cluster_i] = torch.mean(self.original_data[cluster_i], dim=0)

        if len(cluster_j) == 1:
            self.encoded_data[cluster_j] = self.original_data[cluster_j]
        else:
            self.encoded_data[cluster_j] = torch.mean(self.original_data[cluster_j], dim=0)

        return cluster_i, cluster_j

    def fit(self, epoch, cosin_treshold):
        """
        运行分裂聚类的主循环。
        """
        # 根据当前轮次决定本次运行的目标簇数。
        if epoch == 0:
            suspend_sign = self.config['init_clusters']  # 第一轮使用初始聚类数
        else:
            suspend_sign = self.config['cluster_threshold']  # 后续轮次使用聚类上限

        new_cosine_threshold = 0.0  # 初始化一个默认值

        # 第一个循环：持续分裂，直到簇的数量达到 suspend_sign
        while len(self.cluster_queue) < suspend_sign and self.iteration_count < self.max_iterations:
            self.iteration_count += 1

            # 找到内聚性最低（deviation最小）的簇的索引。
            target_cluster_idx = torch.argmin(self.deviation)

            # 在即将达到目标簇数的前一次分裂时，记录下当前最差的内聚性得分。
            if len(self.cluster_queue) == self.config['target_clusters'] - 1:
                new_cosine_threshold = self.deviation[target_cluster_idx]

            # 获取我们将要分裂的簇的成员索引和对应的原始数据。
            cluster_item = self.cluster_queue[target_cluster_idx]
            cluster_original_data = self.original_data[cluster_item]

            # 首先检查阈值：如果当前最不紧密的簇的内聚性得分已经高于阈值，则停止分裂。
            if self.deviation[target_cluster_idx] > cosin_treshold:
                new_encoded_data = self.encoded_data.clone()
                new_num_cluster = len(self.cluster_queue)
                break

            # 然后检查簇大小：如果簇大小小于等于1，跳过分裂
            if len(cluster_item) <= 1:
                # 标记这个簇为高内聚性，避免重复尝试
                self.deviation[target_cluster_idx] = 1.0
                continue

            # 确保簇中有多个点且数据有效
            if len(cluster_item) < 1:
                continue  # 安全起见，跳过空簇

            # 添加额外检查：确保原始数据点数量大于1
            if cluster_original_data.size(0) <= 1:
                self.deviation[target_cluster_idx] = 1.0
                continue

            try:
                # 执行分裂操作。
                cluster_i, cluster_j = self._split_cluster(cluster_item, cluster_original_data)
            except Exception as e:
                print(f"分裂簇时出错: {e}")
                # 标记这个簇分裂失败
                self.deviation[target_cluster_idx] = 1.0
                continue

            # 更新簇队列：用第一个新簇替换掉旧簇，并把第二个新簇追加到队列末尾。
            self.cluster_queue[target_cluster_idx] = cluster_i
            self.cluster_queue.append(cluster_j)

            # --- 为两个新生成的簇更新内聚性得分 ---
            self.deviation = self.deviation.tolist()
            # 计算第一个新簇 (cluster_i) 的内聚性
            if len(cluster_i) == 1:
                self.deviation[target_cluster_idx] = 1.0
            else:
                cluster_original_data_i = self.original_data[cluster_i]
                cluster_encoded_data_i = self.encoded_data[cluster_i]
                self.deviation[target_cluster_idx] = torch.mean(
                    F.cosine_similarity(cluster_original_data_i, cluster_encoded_data_i, dim=1))

            # 计算第二个新簇 (cluster_j) 的内聚性
            if len(cluster_j) == 1:
                self.deviation.append(1.0)
            else:
                cluster_original_data_j = self.original_data[cluster_j]
                cluster_encoded_data_j = self.encoded_data[cluster_j]
                self.deviation.append(
                    torch.mean(F.cosine_similarity(cluster_original_data_j, cluster_encoded_data_j, dim=1)))
            self.deviation = torch.tensor(self.deviation)

        # 检查是否达到最大迭代次数
        if self.iteration_count >= self.max_iterations:
            print(f"警告：聚类达到最大迭代次数 {self.max_iterations}，可能陷入死循环")
            return self.encoded_data, len(self.cluster_queue), new_cosine_threshold

        # 如果循环是因为簇数量达到 suspend_sign 而正常结束，则直接返回结果。
        if len(self.cluster_queue) == suspend_sign:
            if 'new_cosine_threshold' not in locals():
                new_cosine_threshold = 0.0
            return self.encoded_data, len(self.cluster_queue), new_cosine_threshold

        # 如果循环是因为余弦阈值而提前中断，会进入这部分逻辑。
        if 'new_encoded_data' in locals() and len(self.cluster_queue) >= self.config['target_clusters']:
            return new_encoded_data, new_num_cluster, new_cosine_threshold

        # 第二个循环：强制分裂，直到满足 'target_clusters' 的数量。
        while len(self.cluster_queue) < self.config['target_clusters'] and self.iteration_count < self.max_iterations:
            self.iteration_count += 1

            target_cluster_idx = torch.argmin(self.deviation)
            cluster_item = self.cluster_queue[target_cluster_idx]

            # 检查簇大小：如果簇大小小于等于1，跳过分裂
            if len(cluster_item) <= 1:
                # 标记这个簇为高内聚性，避免重复尝试
                self.deviation[target_cluster_idx] = 1.0
                continue

            cluster_original_data = self.original_data[cluster_item]

            # 添加额外检查：确保原始数据点数量大于1
            if cluster_original_data.size(0) <= 1:
                self.deviation[target_cluster_idx] = 1.0
                continue

            # 首先检查阈值：如果当前最不紧密的簇的内聚性得分已经高于阈值，则停止分裂。
            if self.deviation[target_cluster_idx] > cosin_treshold:
                # 如果已经满足目标簇数，直接返回
                if len(self.cluster_queue) >= self.config['target_clusters']:
                    break
                # 否则继续强制分裂
                pass

            if len(self.cluster_queue) == self.config['target_clusters'] - 1:
                new_cosine_threshold = self.deviation[target_cluster_idx]

            try:
                cluster_i, cluster_j = self._split_cluster(cluster_item, cluster_original_data)
            except Exception as e:
                print(f"强制分裂簇时出错: {e}")
                # 标记这个簇分裂失败
                self.deviation[target_cluster_idx] = 1.0
                continue

            self.cluster_queue[target_cluster_idx] = cluster_i
            self.cluster_queue.append(cluster_j)

            # --- 更新内聚性得分（逻辑同上） ---
            self.deviation = self.deviation.tolist()
            if len(cluster_i) == 1:
                self.deviation[target_cluster_idx] = 1.0
            else:
                cluster_original_data_i = self.original_data[cluster_i]
                cluster_encoded_data_i = self.encoded_data[cluster_i]
                self.deviation[target_cluster_idx] = torch.mean(
                    F.cosine_similarity(cluster_original_data_i, cluster_encoded_data_i, dim=1))

            if len(cluster_j) == 1:
                self.deviation.append(1.0)
            else:
                cluster_original_data_j = self.original_data[cluster_j]
                cluster_encoded_data_j = self.encoded_data[cluster_j]
                self.deviation.append(
                    torch.mean(F.cosine_similarity(cluster_original_data_j, cluster_encoded_data_j, dim=1)))
            self.deviation = torch.tensor(self.deviation)

        # 再次检查是否达到最大迭代次数
        if self.iteration_count >= self.max_iterations:
            print(f"警告：聚类达到最大迭代次数 {self.max_iterations}，可能陷入死循环")

        return self.encoded_data, len(self.cluster_queue), new_cosine_threshold