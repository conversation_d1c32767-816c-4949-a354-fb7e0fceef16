{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_16-46-06] - 算法: base_DHC
[Aug-27-2025_16-46-06] - 开始训练，配置参数如下：
[Aug-27-2025_16-46-06] - early_stop_enabled: True
[Aug-27-2025_16-46-06] - early_stop: 30
[Aug-27-2025_16-46-06] - eval_k: 10
[Aug-27-2025_16-46-06] - datapath: ../../data/
[Aug-27-2025_16-46-06] - log_path: ../log
[Aug-27-2025_16-46-06] - assign_by_interactions: False
[Aug-27-2025_16-46-06] - device_split: [0.5, 0.3]
[Aug-27-2025_16-46-06] - dim_s: 16
[Aug-27-2025_16-46-06] - dim_m: 32
[Aug-27-2025_16-46-06] - dim_l: 64
[Aug-27-2025_16-46-06] - device_types: {}
[Aug-27-2025_16-46-06] - quantization_bits: 8
[Aug-27-2025_16-46-06] - quantization_method: symmetric
[Aug-27-2025_16-46-06] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_16-46-06] - kmeans_iterations: 10
[Aug-27-2025_16-46-06] - top_k_ratio: 0.1
[Aug-27-2025_16-46-06] - top_k_method: global
[Aug-27-2025_16-46-06] - min_k: 1
[Aug-27-2025_16-46-06] - use_clustering: True
[Aug-27-2025_16-46-06] - cr: 0.9063
[Aug-27-2025_16-46-06] - cluster_range: 0.2
[Aug-27-2025_16-46-06] - model: SASRec
[Aug-27-2025_16-46-06] - algorithm: base_DHC
[Aug-27-2025_16-46-06] - lr: 0.001
[Aug-27-2025_16-46-06] - batch_size: 128
[Aug-27-2025_16-46-06] - l2_reg: 0
[Aug-27-2025_16-46-06] - l2_emb: 0.0
[Aug-27-2025_16-46-06] - hidden_size: 64
[Aug-27-2025_16-46-06] - dropout: 0.2
[Aug-27-2025_16-46-06] - epochs: 1000000
[Aug-27-2025_16-46-06] - dataset: ml-100k
[Aug-27-2025_16-46-06] - train_data: ml-100k.txt
[Aug-27-2025_16-46-06] - num_layers: 2
[Aug-27-2025_16-46-06] - num_heads: 1
[Aug-27-2025_16-46-06] - inner_size: 256
[Aug-27-2025_16-46-06] - max_seq_len: 200
[Aug-27-2025_16-46-06] - decor_alpha: 0.3
[Aug-27-2025_16-46-06] - neg_num: 99
[Aug-27-2025_16-46-06] - skip_test_eval: True
[Aug-27-2025_16-46-06] - eval_freq: 1
[Aug-27-2025_16-46-06] - full_eval: False
[Aug-27-2025_16-46-06] - c: 9
[Aug-27-2025_16-46-06] - alpha: 0.3
[Aug-27-2025_16-46-06] - kd_ratio: 0.1
[Aug-27-2025_16-46-06] - kd_lr: 0.001
[Aug-27-2025_16-46-06] - distill_epochs: 10
[Aug-27-2025_16-46-06] - distill_freq: 3
[Aug-27-2025_16-46-06] - max_iterations: 1000
[Aug-27-2025_16-46-06] - target_clusters: 105
[Aug-27-2025_16-46-06] - quantize_gradients: True
[Aug-27-2025_16-46-06] - quantization_type: uniform
[Aug-27-2025_16-46-06] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_16-46-06] - 最大序列长度: 200
[Aug-27-2025_16-46-06] - 批次大小: 128
[Aug-27-2025_16-46-07] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_16-46-07] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_16-46-07] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_16-46-07] - 用户数量: 943
[Aug-27-2025_16-46-07] - 物品数量: 1349
[Aug-27-2025_16-46-08] - 梯度聚类功能已启用: 目标聚类数(C_e)=126, 初始聚类数(C_i)=100, 聚类上限(C_m)=151
[Aug-27-2025_16-46-08] - 服务器初始化完成
[Aug-27-2025_16-46-08] - 初始化通信开销计算器...
[Aug-27-2025_16-46-08] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-27-2025_16-46-08] - Embedding层尺寸 (MB): S=0.086, M=0.173, L=0.346
[Aug-27-2025_16-46-13] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-15] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-18] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-20] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-23] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-26] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-28] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-46-29] - 代表性客户端数量: S=1, M=1, L=1
[Aug-27-2025_16-46-34] - COMMUNICATION COST - Epoch 1: Downlink=717.150MB, Uplink=181.736MB, Total=898.886MB
[Aug-27-2025_16-46-34] - 传统评估结果 - Epoch 1: NDCG@10=0.0843, HR@10=0.1676
[Aug-27-2025_16-46-34] - epoch:1, time: 21.069362(s), valid (NDCG@10: 0.0843, HR@10: 0.1676), test: SKIPPED, all_time: 21.069362(s)
[Aug-27-2025_16-46-34] - 新的最佳性能: valid NDCG@10=0.0843, valid HR@10=0.1676
[Aug-27-2025_16-46-37] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-46-41] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-46-43] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-46-46] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-46-49] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-46-51] - 代表性客户端数量: S=5, M=3, L=2
