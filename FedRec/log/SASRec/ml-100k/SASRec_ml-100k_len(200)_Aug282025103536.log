{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.3, 'top_k_method': 'layer-wise', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Q', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-28-2025_10-35-36] - 算法: base_Q
[Aug-28-2025_10-35-36] - 开始训练，配置参数如下：
[Aug-28-2025_10-35-36] - early_stop_enabled: True
[Aug-28-2025_10-35-36] - early_stop: 30
[Aug-28-2025_10-35-36] - eval_k: 10
[Aug-28-2025_10-35-36] - datapath: ../../data/
[Aug-28-2025_10-35-36] - log_path: ../log
[Aug-28-2025_10-35-36] - assign_by_interactions: False
[Aug-28-2025_10-35-36] - device_split: [0.5, 0.3]
[Aug-28-2025_10-35-36] - dim_s: 16
[Aug-28-2025_10-35-36] - dim_m: 32
[Aug-28-2025_10-35-36] - dim_l: 64
[Aug-28-2025_10-35-36] - device_types: {}
[Aug-28-2025_10-35-36] - quantization_bits: 8
[Aug-28-2025_10-35-36] - quantization_method: symmetric
[Aug-28-2025_10-35-36] - adaptive_percentile: [0.01, 0.99]
[Aug-28-2025_10-35-36] - kmeans_iterations: 10
[Aug-28-2025_10-35-36] - top_k_ratio: 0.3
[Aug-28-2025_10-35-36] - top_k_method: layer-wise
[Aug-28-2025_10-35-36] - min_k: 1
[Aug-28-2025_10-35-36] - use_clustering: True
[Aug-28-2025_10-35-36] - cr: 0.9063
[Aug-28-2025_10-35-36] - cluster_range: 0.2
[Aug-28-2025_10-35-36] - model: SASRec
[Aug-28-2025_10-35-36] - algorithm: base_Q
[Aug-28-2025_10-35-36] - lr: 0.001
[Aug-28-2025_10-35-36] - batch_size: 128
[Aug-28-2025_10-35-36] - l2_reg: 0
[Aug-28-2025_10-35-36] - l2_emb: 0.0
[Aug-28-2025_10-35-36] - hidden_size: 64
[Aug-28-2025_10-35-36] - dropout: 0.2
[Aug-28-2025_10-35-36] - epochs: 1000000
[Aug-28-2025_10-35-36] - dataset: ml-100k
[Aug-28-2025_10-35-36] - train_data: ml-100k.txt
[Aug-28-2025_10-35-36] - num_layers: 2
[Aug-28-2025_10-35-36] - num_heads: 1
[Aug-28-2025_10-35-36] - inner_size: 256
[Aug-28-2025_10-35-36] - max_seq_len: 200
[Aug-28-2025_10-35-36] - decor_alpha: 0.3
[Aug-28-2025_10-35-36] - neg_num: 99
[Aug-28-2025_10-35-36] - skip_test_eval: True
[Aug-28-2025_10-35-36] - eval_freq: 1
[Aug-28-2025_10-35-36] - full_eval: False
[Aug-28-2025_10-35-36] - c: 9
[Aug-28-2025_10-35-36] - alpha: 0.3
[Aug-28-2025_10-35-36] - kd_ratio: 0.1
[Aug-28-2025_10-35-36] - kd_lr: 0.001
[Aug-28-2025_10-35-36] - distill_epochs: 10
[Aug-28-2025_10-35-36] - distill_freq: 3
[Aug-28-2025_10-35-36] - max_iterations: 1000
[Aug-28-2025_10-35-36] - target_clusters: 105
[Aug-28-2025_10-35-36] - quantize_gradients: True
[Aug-28-2025_10-35-36] - quantization_type: uniform
[Aug-28-2025_10-35-36] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-28-2025_10-35-36] - 最大序列长度: 200
[Aug-28-2025_10-35-36] - 批次大小: 128
[Aug-28-2025_10-35-37] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-28-2025_10-35-37] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-28-2025_10-35-37] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-28-2025_10-35-37] - 启用梯度量化 (双向): 8位, 类型: uniform
[Aug-28-2025_10-35-37] - 用户数量: 943
[Aug-28-2025_10-35-37] - 物品数量: 1349
[Aug-28-2025_10-35-39] - 服务器初始化完成
[Aug-28-2025_10-35-39] - 初始化通信开销计算器...
[Aug-28-2025_10-35-39] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-28-2025_10-36-24] - COMMUNICATION COST - Epoch 1: Downlink=179.417MB, Uplink=80.624MB, Total=260.041MB
[Aug-28-2025_10-36-24] - 传统评估结果 - Epoch 1: NDCG@10=0.0692, HR@10=0.1474
[Aug-28-2025_10-36-24] - epoch:1, time: 39.289433(s), valid (NDCG@10: 0.0692, HR@10: 0.1474), test: SKIPPED, all_time: 39.289433(s)
[Aug-28-2025_10-36-24] - 新的最佳性能: valid NDCG@10=0.0692, valid HR@10=0.1474
[Aug-28-2025_10-36-24] - [联邦训练] 最佳结果: valid NDCG@10=0.0692, HR@10=0.1474 (测试集评估已跳过)
[Aug-28-2025_10-37-07] - COMMUNICATION COST - Epoch 2: Downlink=358.834MB, Uplink=161.247MB, Total=520.081MB
[Aug-28-2025_10-37-07] - 传统评估结果 - Epoch 2: NDCG@10=0.1187, HR@10=0.2354
[Aug-28-2025_10-37-07] - epoch:2, time: 38.424454(s), valid (NDCG@10: 0.1187, HR@10: 0.2354), test: SKIPPED, all_time: 77.713887(s)
[Aug-28-2025_10-37-07] - 新的最佳性能: valid NDCG@10=0.1187, valid HR@10=0.2354
[Aug-28-2025_10-37-07] - [联邦训练] 最佳结果: valid NDCG@10=0.1187, HR@10=0.2354 (测试集评估已跳过)
[Aug-28-2025_10-37-57] - COMMUNICATION COST - Epoch 3: Downlink=538.251MB, Uplink=241.871MB, Total=780.122MB
[Aug-28-2025_10-37-57] - 传统评估结果 - Epoch 3: NDCG@10=0.1585, HR@10=0.3160
[Aug-28-2025_10-37-57] - epoch:3, time: 43.630945(s), valid (NDCG@10: 0.1585, HR@10: 0.3160), test: SKIPPED, all_time: 121.344832(s)
[Aug-28-2025_10-37-57] - 新的最佳性能: valid NDCG@10=0.1585, valid HR@10=0.3160
[Aug-28-2025_10-37-57] - [联邦训练] 最佳结果: valid NDCG@10=0.1585, HR@10=0.3160 (测试集评估已跳过)
[Aug-28-2025_10-38-42] - COMMUNICATION COST - Epoch 4: Downlink=717.668MB, Uplink=322.495MB, Total=1040.162MB
[Aug-28-2025_10-38-42] - 传统评估结果 - Epoch 4: NDCG@10=0.1669, HR@10=0.3383
[Aug-28-2025_10-38-42] - epoch:4, time: 40.938450(s), valid (NDCG@10: 0.1669, HR@10: 0.3383), test: SKIPPED, all_time: 162.282261(s)
[Aug-28-2025_10-38-42] - 新的最佳性能: valid NDCG@10=0.1669, valid HR@10=0.3383
[Aug-28-2025_10-38-42] - [联邦训练] 最佳结果: valid NDCG@10=0.1669, HR@10=0.3383 (测试集评估已跳过)
