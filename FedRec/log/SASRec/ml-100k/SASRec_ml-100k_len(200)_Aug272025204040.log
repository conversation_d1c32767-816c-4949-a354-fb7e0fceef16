{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Top_k', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_20-40-40] - 算法: base_Top_k
[Aug-27-2025_20-40-40] - 开始训练，配置参数如下：
[Aug-27-2025_20-40-40] - early_stop_enabled: True
[Aug-27-2025_20-40-40] - early_stop: 30
[Aug-27-2025_20-40-40] - eval_k: 10
[Aug-27-2025_20-40-40] - datapath: ../../data/
[Aug-27-2025_20-40-40] - log_path: ../log
[Aug-27-2025_20-40-40] - assign_by_interactions: False
[Aug-27-2025_20-40-40] - device_split: [0.5, 0.3]
[Aug-27-2025_20-40-40] - dim_s: 16
[Aug-27-2025_20-40-40] - dim_m: 32
[Aug-27-2025_20-40-40] - dim_l: 64
[Aug-27-2025_20-40-40] - device_types: {}
[Aug-27-2025_20-40-40] - quantization_bits: 8
[Aug-27-2025_20-40-40] - quantization_method: symmetric
[Aug-27-2025_20-40-40] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_20-40-40] - kmeans_iterations: 10
[Aug-27-2025_20-40-40] - top_k_ratio: 0.1
[Aug-27-2025_20-40-40] - top_k_method: global
[Aug-27-2025_20-40-40] - min_k: 1
[Aug-27-2025_20-40-40] - use_clustering: True
[Aug-27-2025_20-40-40] - cr: 0.9063
[Aug-27-2025_20-40-40] - cluster_range: 0.2
[Aug-27-2025_20-40-40] - model: SASRec
[Aug-27-2025_20-40-40] - algorithm: base_Top_k
[Aug-27-2025_20-40-40] - lr: 0.001
[Aug-27-2025_20-40-40] - batch_size: 256
[Aug-27-2025_20-40-40] - l2_reg: 0
[Aug-27-2025_20-40-40] - l2_emb: 0.0
[Aug-27-2025_20-40-40] - hidden_size: 64
[Aug-27-2025_20-40-40] - dropout: 0.2
[Aug-27-2025_20-40-40] - epochs: 1000000
[Aug-27-2025_20-40-40] - dataset: ml-100k
[Aug-27-2025_20-40-40] - train_data: ml-100k.txt
[Aug-27-2025_20-40-40] - num_layers: 2
[Aug-27-2025_20-40-40] - num_heads: 1
[Aug-27-2025_20-40-40] - inner_size: 256
[Aug-27-2025_20-40-40] - max_seq_len: 200
[Aug-27-2025_20-40-40] - decor_alpha: 0.3
[Aug-27-2025_20-40-40] - neg_num: 99
[Aug-27-2025_20-40-40] - skip_test_eval: True
[Aug-27-2025_20-40-40] - eval_freq: 1
[Aug-27-2025_20-40-40] - full_eval: False
[Aug-27-2025_20-40-40] - c: 9
[Aug-27-2025_20-40-40] - alpha: 0.3
[Aug-27-2025_20-40-40] - kd_ratio: 0.1
[Aug-27-2025_20-40-40] - kd_lr: 0.001
[Aug-27-2025_20-40-40] - distill_epochs: 10
[Aug-27-2025_20-40-40] - distill_freq: 3
[Aug-27-2025_20-40-40] - max_iterations: 1000
[Aug-27-2025_20-40-40] - target_clusters: 105
[Aug-27-2025_20-40-40] - quantize_gradients: True
[Aug-27-2025_20-40-40] - quantization_type: uniform
[Aug-27-2025_20-40-40] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_20-40-40] - 最大序列长度: 200
[Aug-27-2025_20-40-40] - 批次大小: 256
[Aug-27-2025_20-40-41] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_20-40-41] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_20-40-41] - Top-k配置: 比例=0.1, 方法=global, 最小k=1
[Aug-27-2025_20-40-41] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_20-40-41] - 用户数量: 943
[Aug-27-2025_20-40-41] - 物品数量: 1349
[Aug-27-2025_20-40-42] - 服务器初始化完成
[Aug-27-2025_20-41-13] - Epoch 1 通信开销: 上行 = 965.9302 MB, 下行 = 717.1497 MB, 总计 = 1683.0799 MB
[Aug-27-2025_20-41-17] - 传统评估结果 - Epoch 1: NDCG@10=0.0566, HR@10=0.1273
[Aug-27-2025_20-41-17] - Top-k压缩统计: 总梯度数=16296112, 选择梯度数=16296112, 压缩比例=100.00%
[Aug-27-2025_20-41-17] - epoch:1, time: 31.457501(s), valid (NDCG@10: 0.0566, HR@10: 0.1273), test: SKIPPED, all_time: 31.457501(s)
[Aug-27-2025_20-41-17] - 新的最佳性能: valid NDCG@10=0.0566, valid HR@10=0.1273
[Aug-27-2025_20-41-50] - Epoch 2 通信开销: 上行 = 965.9302 MB, 下行 = 717.1497 MB, 总计 = 1683.0799 MB
[Aug-27-2025_20-41-54] - 传统评估结果 - Epoch 2: NDCG@10=0.0745, HR@10=0.1612
[Aug-27-2025_20-41-54] - Top-k压缩统计: 总梯度数=16296112, 选择梯度数=16296112, 压缩比例=100.00%
[Aug-27-2025_20-41-54] - epoch:2, time: 33.022753(s), valid (NDCG@10: 0.0745, HR@10: 0.1612), test: SKIPPED, all_time: 64.480254(s)
[Aug-27-2025_20-41-54] - 新的最佳性能: valid NDCG@10=0.0745, valid HR@10=0.1612
[Aug-27-2025_20-42-27] - Epoch 3 通信开销: 上行 = 965.9302 MB, 下行 = 717.1497 MB, 总计 = 1683.0799 MB
[Aug-27-2025_20-42-31] - 传统评估结果 - Epoch 3: NDCG@10=0.0879, HR@10=0.1888
[Aug-27-2025_20-42-31] - Top-k压缩统计: 总梯度数=16296112, 选择梯度数=16296112, 压缩比例=100.00%
[Aug-27-2025_20-42-31] - epoch:3, time: 32.531340(s), valid (NDCG@10: 0.0879, HR@10: 0.1888), test: SKIPPED, all_time: 97.011594(s)
[Aug-27-2025_20-42-31] - 新的最佳性能: valid NDCG@10=0.0879, valid HR@10=0.1888
