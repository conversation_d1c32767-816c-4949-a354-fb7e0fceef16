{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-26-2025_23-29-06] - 算法: base_DHC
[Aug-26-2025_23-29-06] - 开始训练，配置参数如下：
[Aug-26-2025_23-29-06] - early_stop_enabled: True
[Aug-26-2025_23-29-06] - early_stop: 30
[Aug-26-2025_23-29-06] - eval_k: 10
[Aug-26-2025_23-29-06] - datapath: ../../data/
[Aug-26-2025_23-29-06] - log_path: ../log
[Aug-26-2025_23-29-06] - assign_by_interactions: False
[Aug-26-2025_23-29-06] - device_split: [0.5, 0.3]
[Aug-26-2025_23-29-06] - dim_s: 16
[Aug-26-2025_23-29-06] - dim_m: 32
[Aug-26-2025_23-29-06] - dim_l: 64
[Aug-26-2025_23-29-06] - device_types: {}
[Aug-26-2025_23-29-06] - quantization_bits: 8
[Aug-26-2025_23-29-06] - quantization_method: symmetric
[Aug-26-2025_23-29-06] - adaptive_percentile: [0.01, 0.99]
[Aug-26-2025_23-29-06] - kmeans_iterations: 10
[Aug-26-2025_23-29-06] - top_k_ratio: 0.1
[Aug-26-2025_23-29-06] - top_k_method: global
[Aug-26-2025_23-29-06] - min_k: 1
[Aug-26-2025_23-29-06] - use_clustering: True
[Aug-26-2025_23-29-06] - cr: 0.9063
[Aug-26-2025_23-29-06] - cluster_range: 0.2
[Aug-26-2025_23-29-06] - model: SASRec
[Aug-26-2025_23-29-06] - algorithm: base_DHC
[Aug-26-2025_23-29-06] - lr: 0.001
[Aug-26-2025_23-29-06] - batch_size: 128
[Aug-26-2025_23-29-06] - l2_reg: 0
[Aug-26-2025_23-29-06] - l2_emb: 0.0
[Aug-26-2025_23-29-06] - hidden_size: 64
[Aug-26-2025_23-29-06] - dropout: 0.2
[Aug-26-2025_23-29-06] - epochs: 1000000
[Aug-26-2025_23-29-06] - dataset: ml-100k
[Aug-26-2025_23-29-06] - train_data: ml-100k.txt
[Aug-26-2025_23-29-06] - num_layers: 2
[Aug-26-2025_23-29-06] - num_heads: 1
[Aug-26-2025_23-29-06] - inner_size: 256
[Aug-26-2025_23-29-06] - max_seq_len: 200
[Aug-26-2025_23-29-06] - decor_alpha: 0.3
[Aug-26-2025_23-29-06] - neg_num: 99
[Aug-26-2025_23-29-06] - skip_test_eval: True
[Aug-26-2025_23-29-06] - eval_freq: 1
[Aug-26-2025_23-29-06] - full_eval: False
[Aug-26-2025_23-29-06] - c: 9
[Aug-26-2025_23-29-06] - alpha: 0.3
[Aug-26-2025_23-29-06] - kd_ratio: 0.1
[Aug-26-2025_23-29-06] - kd_lr: 0.001
[Aug-26-2025_23-29-06] - distill_epochs: 10
[Aug-26-2025_23-29-06] - distill_freq: 3
[Aug-26-2025_23-29-06] - max_iterations: 1000
[Aug-26-2025_23-29-06] - target_clusters: 105
[Aug-26-2025_23-29-06] - quantize_gradients: True
[Aug-26-2025_23-29-06] - quantization_type: uniform
[Aug-26-2025_23-29-06] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-26-2025_23-29-06] - 最大序列长度: 200
[Aug-26-2025_23-29-06] - 批次大小: 128
[Aug-26-2025_23-29-07] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-26-2025_23-29-07] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-26-2025_23-29-07] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-26-2025_23-29-07] - 用户数量: 943
[Aug-26-2025_23-29-07] - 物品数量: 1349
[Aug-26-2025_23-29-09] - 梯度聚类功能已启用: 目标聚类数(C_e)=126, 初始聚类数(C_i)=100, 聚类上限(C_m)=151
[Aug-26-2025_23-29-09] - 服务器初始化完成
[Aug-26-2025_23-29-12] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-14] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-14] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-17] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-20] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-22] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-29-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-22] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-25] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-29-25] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-25] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_23-29-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-28] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-31] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_23-29-32] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-32] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-32] - 梯度聚类完成: 47 -> 3个客户端
[Aug-26-2025_23-29-37] - 传统评估结果 - Epoch 1: NDCG@10=0.0706, HR@10=0.1591
[Aug-26-2025_23-29-37] - epoch:1, time: 23.259629(s), valid (NDCG@10: 0.0706, HR@10: 0.1591), test: SKIPPED, all_time: 23.259629(s)
[Aug-26-2025_23-29-37] - 新的最佳性能: valid NDCG@10=0.0706, valid HR@10=0.1591
[Aug-26-2025_23-29-39] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-29-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-29-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-29-47] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-29-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-29-50] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-29-50] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-29-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-29-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-53] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-29-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-29-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-29-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-56] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-29-57] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-29-57] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-29-57] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-30-02] - 传统评估结果 - Epoch 2: NDCG@10=0.1201, HR@10=0.2460
[Aug-26-2025_23-30-02] - epoch:2, time: 20.523852(s), valid (NDCG@10: 0.1201, HR@10: 0.2460), test: SKIPPED, all_time: 43.783481(s)
[Aug-26-2025_23-30-02] - 新的最佳性能: valid NDCG@10=0.1201, valid HR@10=0.2460
[Aug-26-2025_23-30-05] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-05] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-30-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-17] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-30-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-24] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-25] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-25] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-26] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-30-30] - 传统评估结果 - Epoch 3: NDCG@10=0.1503, HR@10=0.2959
[Aug-26-2025_23-30-30] - epoch:3, time: 23.586208(s), valid (NDCG@10: 0.1503, HR@10: 0.2959), test: SKIPPED, all_time: 67.369688(s)
[Aug-26-2025_23-30-30] - 新的最佳性能: valid NDCG@10=0.1503, valid HR@10=0.2959
[Aug-26-2025_23-30-33] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-33] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-33] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-42] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-30-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-45] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-30-45] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-30-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-48] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-48] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-30-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-30-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-30-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-51] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-30-52] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-30-52] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-52] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-30-52] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-30-57] - 传统评估结果 - Epoch 4: NDCG@10=0.1626, HR@10=0.3139
[Aug-26-2025_23-30-57] - epoch:4, time: 21.607234(s), valid (NDCG@10: 0.1626, HR@10: 0.3139), test: SKIPPED, all_time: 88.976922(s)
[Aug-26-2025_23-30-57] - 新的最佳性能: valid NDCG@10=0.1626, valid HR@10=0.3139
[Aug-26-2025_23-31-00] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-00] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-03] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-09] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-31-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-31-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-23] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-23] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-23] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-23] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-31-29] - 传统评估结果 - Epoch 5: NDCG@10=0.1725, HR@10=0.3372
[Aug-26-2025_23-31-29] - epoch:5, time: 25.835809(s), valid (NDCG@10: 0.1725, HR@10: 0.3372), test: SKIPPED, all_time: 114.812731(s)
[Aug-26-2025_23-31-29] - 新的最佳性能: valid NDCG@10=0.1725, valid HR@10=0.3372
[Aug-26-2025_23-31-32] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-32] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-32] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-43] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-31-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-47] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-31-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-31-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-31-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-31-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-31-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-31-55] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-31-55] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-31-55] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-32-01] - 传统评估结果 - Epoch 6: NDCG@10=0.1821, HR@10=0.3552
[Aug-26-2025_23-32-01] - epoch:6, time: 25.855795(s), valid (NDCG@10: 0.1821, HR@10: 0.3552), test: SKIPPED, all_time: 140.668526(s)
[Aug-26-2025_23-32-01] - 新的最佳性能: valid NDCG@10=0.1821, valid HR@10=0.3552
[Aug-26-2025_23-32-03] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-32-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-16] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-32-16] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-19] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-22] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-24] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-24] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-24] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-32-28] - 传统评估结果 - Epoch 7: NDCG@10=0.1798, HR@10=0.3478
[Aug-26-2025_23-32-28] - epoch:7, time: 23.217880(s), valid (NDCG@10: 0.1798, HR@10: 0.3478), test: SKIPPED, all_time: 163.886406(s)
[Aug-26-2025_23-32-31] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-31] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-34] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-38] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-38] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-32-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-45] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-32-45] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-32-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-48] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-48] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-32-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-32-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-32-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-51] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-32-52] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-32-52] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-52] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-32-52] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-32-59] - 传统评估结果 - Epoch 8: NDCG@10=0.1776, HR@10=0.3446
[Aug-26-2025_23-32-59] - epoch:8, time: 24.024203(s), valid (NDCG@10: 0.1776, HR@10: 0.3446), test: SKIPPED, all_time: 187.910609(s)
[Aug-26-2025_23-33-02] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-02] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-06] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-06] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-33-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-16] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-33-16] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-19] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-23] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-24] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-24] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-24] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-33-29] - 传统评估结果 - Epoch 9: NDCG@10=0.1833, HR@10=0.3489
[Aug-26-2025_23-33-29] - epoch:9, time: 25.097580(s), valid (NDCG@10: 0.1833, HR@10: 0.3489), test: SKIPPED, all_time: 213.008189(s)
[Aug-26-2025_23-33-31] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-31] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-34] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-38] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-33-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-43] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-33-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-46] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-33-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-33-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-33-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-49] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-33-51] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-33-51] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-33-51] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-33-56] - 传统评估结果 - Epoch 10: NDCG@10=0.1805, HR@10=0.3340
[Aug-26-2025_23-33-56] - epoch:10, time: 22.381475(s), valid (NDCG@10: 0.1805, HR@10: 0.3340), test: SKIPPED, all_time: 235.389663(s)
[Aug-26-2025_23-33-59] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-59] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-33-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-03] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-09] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-34-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-34-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-34-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-20] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-21] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-21] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-21] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-34-27] - 传统评估结果 - Epoch 11: NDCG@10=0.1833, HR@10=0.3521
[Aug-26-2025_23-34-27] - epoch:11, time: 24.701583(s), valid (NDCG@10: 0.1833, HR@10: 0.3521), test: SKIPPED, all_time: 260.091247(s)
[Aug-26-2025_23-34-30] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-34-30] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-34-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-40] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-34-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-34-44] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-34-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-34-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-34-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-34-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-51] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-34-53] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-34-53] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-53] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-34-53] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-34-58] - 传统评估结果 - Epoch 12: NDCG@10=0.1803, HR@10=0.3404
[Aug-26-2025_23-34-58] - epoch:12, time: 25.592060(s), valid (NDCG@10: 0.1803, HR@10: 0.3404), test: SKIPPED, all_time: 285.683307(s)
[Aug-26-2025_23-35-01] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-01] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-04] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-14] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-35-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-35-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-26] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-26] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-26] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-35-32] - 传统评估结果 - Epoch 13: NDCG@10=0.1779, HR@10=0.3256
[Aug-26-2025_23-35-32] - epoch:13, time: 28.258214(s), valid (NDCG@10: 0.1779, HR@10: 0.3256), test: SKIPPED, all_time: 313.941521(s)
[Aug-26-2025_23-35-34] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-34] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-35-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-47] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-35-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-35-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-35-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-35-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-35-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-35-55] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-35-55] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-35-55] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-36-00] - 传统评估结果 - Epoch 14: NDCG@10=0.1904, HR@10=0.3521
[Aug-26-2025_23-36-00] - epoch:14, time: 23.501331(s), valid (NDCG@10: 0.1904, HR@10: 0.3521), test: SKIPPED, all_time: 337.442852(s)
[Aug-26-2025_23-36-03] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-11] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-36-15] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-36-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-36-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-36-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-36-26] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-26] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-26] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-36-31] - 传统评估结果 - Epoch 15: NDCG@10=0.2078, HR@10=0.3839
[Aug-26-2025_23-36-31] - epoch:15, time: 25.612296(s), valid (NDCG@10: 0.2078, HR@10: 0.3839), test: SKIPPED, all_time: 363.055147(s)
[Aug-26-2025_23-36-31] - 新的最佳性能: valid NDCG@10=0.2078, valid HR@10=0.3839
[Aug-26-2025_23-36-34] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-34] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-35] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-39] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-36-46] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-36-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-46] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-50] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-36-50] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-36-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-36-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-36-54] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-36-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-36-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-36-58] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-36-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-37-00] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-00] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-00] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-37-08] - 传统评估结果 - Epoch 16: NDCG@10=0.2138, HR@10=0.3849
[Aug-26-2025_23-37-08] - epoch:16, time: 29.796703(s), valid (NDCG@10: 0.2138, HR@10: 0.3849), test: SKIPPED, all_time: 392.851851(s)
[Aug-26-2025_23-37-08] - 新的最佳性能: valid NDCG@10=0.2138, valid HR@10=0.3849
[Aug-26-2025_23-37-12] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-12] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-37-23] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-37-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-23] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-26] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-37-26] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-26] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-37-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-37-34] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-34] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-34] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-34] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-37-40] - 传统评估结果 - Epoch 17: NDCG@10=0.2221, HR@10=0.3955
[Aug-26-2025_23-37-40] - epoch:17, time: 26.165778(s), valid (NDCG@10: 0.2221, HR@10: 0.3955), test: SKIPPED, all_time: 419.017628(s)
[Aug-26-2025_23-37-40] - 新的最佳性能: valid NDCG@10=0.2221, valid HR@10=0.3955
[Aug-26-2025_23-37-43] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-37-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-37-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-51] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-37-54] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-37-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-37-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-54] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-37-58] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-37-58] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-37-58] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-37-58] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-02] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-05] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-07] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-07] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-07] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-38-13] - 传统评估结果 - Epoch 18: NDCG@10=0.2265, HR@10=0.4083
[Aug-26-2025_23-38-13] - epoch:18, time: 26.840305(s), valid (NDCG@10: 0.2265, HR@10: 0.4083), test: SKIPPED, all_time: 445.857933(s)
[Aug-26-2025_23-38-13] - 新的最佳性能: valid NDCG@10=0.2265, valid HR@10=0.4083
[Aug-26-2025_23-38-16] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-16] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-19] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-24] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-38-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-27] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-38-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-34] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-35] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-35] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-35] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-35] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-38-41] - 传统评估结果 - Epoch 19: NDCG@10=0.2385, HR@10=0.4284
[Aug-26-2025_23-38-41] - epoch:19, time: 22.308962(s), valid (NDCG@10: 0.2385, HR@10: 0.4284), test: SKIPPED, all_time: 468.166895(s)
[Aug-26-2025_23-38-41] - 新的最佳性能: valid NDCG@10=0.2385, valid HR@10=0.4284
[Aug-26-2025_23-38-44] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-44] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-38-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-38-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-38-53] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-38-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-38-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-38-57] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-38-57] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-38-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-38-57] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-06] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-06] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-06] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-06] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-39-12] - 传统评估结果 - Epoch 20: NDCG@10=0.2444, HR@10=0.4178
[Aug-26-2025_23-39-12] - epoch:20, time: 25.784555(s), valid (NDCG@10: 0.2444, HR@10: 0.4178), test: SKIPPED, all_time: 493.951450(s)
[Aug-26-2025_23-39-15] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-39-15] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-39-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-24] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-39-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-27] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-39-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-39-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-35] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-37] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-37] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-37] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-39-44] - 传统评估结果 - Epoch 21: NDCG@10=0.2488, HR@10=0.4422
[Aug-26-2025_23-39-44] - epoch:21, time: 24.767819(s), valid (NDCG@10: 0.2488, HR@10: 0.4422), test: SKIPPED, all_time: 518.719269(s)
[Aug-26-2025_23-39-44] - 新的最佳性能: valid NDCG@10=0.2488, valid HR@10=0.4422
[Aug-26-2025_23-39-47] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-39-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-39-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-39-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-39-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-39-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-39-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-39-55] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-39-59] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-40-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-03] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-40-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-03] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-40-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-40-13] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-13] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-13] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-40-19] - 传统评估结果 - Epoch 22: NDCG@10=0.2542, HR@10=0.4454
[Aug-26-2025_23-40-19] - epoch:22, time: 29.658049(s), valid (NDCG@10: 0.2542, HR@10: 0.4454), test: SKIPPED, all_time: 548.377318(s)
[Aug-26-2025_23-40-19] - 新的最佳性能: valid NDCG@10=0.2542, valid HR@10=0.4454
[Aug-26-2025_23-40-21] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-21] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-40-31] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-40-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-40-36] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-36] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-40-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-39] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-40-43] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-43] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-43] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-43] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-40-49] - 传统评估结果 - Epoch 23: NDCG@10=0.2571, HR@10=0.4602
[Aug-26-2025_23-40-49] - epoch:23, time: 24.740441(s), valid (NDCG@10: 0.2571, HR@10: 0.4602), test: SKIPPED, all_time: 573.117759(s)
[Aug-26-2025_23-40-49] - 新的最佳性能: valid NDCG@10=0.2571, valid HR@10=0.4602
[Aug-26-2025_23-40-51] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-51] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-40-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-40-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-40-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-40-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-40-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-40-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-03] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-41-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-06] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-41-06] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-10] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-14] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-14] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-14] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-41-20] - 传统评估结果 - Epoch 24: NDCG@10=0.2556, HR@10=0.4507
[Aug-26-2025_23-41-20] - epoch:24, time: 25.660110(s), valid (NDCG@10: 0.2556, HR@10: 0.4507), test: SKIPPED, all_time: 598.777869(s)
[Aug-26-2025_23-41-23] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-23] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-23] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-30] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-34] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-41-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-37] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-41-37] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-41-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-41-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-41-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-43] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-41-44] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-44] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-41-44] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-41-50] - 传统评估结果 - Epoch 25: NDCG@10=0.2705, HR@10=0.4825
[Aug-26-2025_23-41-50] - epoch:25, time: 23.722440(s), valid (NDCG@10: 0.2705, HR@10: 0.4825), test: SKIPPED, all_time: 622.500309(s)
[Aug-26-2025_23-41-50] - 新的最佳性能: valid NDCG@10=0.2705, valid HR@10=0.4825
[Aug-26-2025_23-41-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-41-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-41-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-01] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-05] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-09] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-42-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-42-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-42-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-22] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-22] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-22] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-42-27] - 传统评估结果 - Epoch 26: NDCG@10=0.2674, HR@10=0.4836
[Aug-26-2025_23-42-27] - epoch:26, time: 32.184018(s), valid (NDCG@10: 0.2674, HR@10: 0.4836), test: SKIPPED, all_time: 654.684327(s)
[Aug-26-2025_23-42-27] - 新的最佳性能: valid NDCG@10=0.2705, valid HR@10=0.4836
[Aug-26-2025_23-42-30] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-42-30] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-42-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-33] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-42-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-42-44] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-42-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-49] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-49] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-42-52] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-42-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-42-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-52] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-42-54] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-42-54] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-42-54] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-43-00] - 传统评估结果 - Epoch 27: NDCG@10=0.2821, HR@10=0.4931
[Aug-26-2025_23-43-00] - epoch:27, time: 26.378901(s), valid (NDCG@10: 0.2821, HR@10: 0.4931), test: SKIPPED, all_time: 681.063229(s)
[Aug-26-2025_23-43-00] - 新的最佳性能: valid NDCG@10=0.2821, valid HR@10=0.4931
[Aug-26-2025_23-43-03] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-14] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-43-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-14] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-43-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-26] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-26] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-26] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-43-31] - 传统评估结果 - Epoch 28: NDCG@10=0.2804, HR@10=0.5069
[Aug-26-2025_23-43-31] - epoch:28, time: 26.337293(s), valid (NDCG@10: 0.2804, HR@10: 0.5069), test: SKIPPED, all_time: 707.400522(s)
[Aug-26-2025_23-43-31] - 新的最佳性能: valid NDCG@10=0.2821, valid HR@10=0.5069
[Aug-26-2025_23-43-34] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-34] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-43] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-43-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-46] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-43-46] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-43-46] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-46] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-49] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-49] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-43-52] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-43-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-43-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-52] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-43-53] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-43-53] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-53] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-43-53] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-44-00] - 传统评估结果 - Epoch 29: NDCG@10=0.2803, HR@10=0.4910
[Aug-26-2025_23-44-00] - epoch:29, time: 22.391490(s), valid (NDCG@10: 0.2803, HR@10: 0.4910), test: SKIPPED, all_time: 729.792011(s)
[Aug-26-2025_23-44-03] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-04] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-15] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-44-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-20] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-44-20] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-20] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-28] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-30] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-30] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-30] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-44-35] - 传统评估结果 - Epoch 30: NDCG@10=0.2964, HR@10=0.5186
[Aug-26-2025_23-44-35] - epoch:30, time: 29.272127(s), valid (NDCG@10: 0.2964, HR@10: 0.5186), test: SKIPPED, all_time: 759.064139(s)
[Aug-26-2025_23-44-35] - 新的最佳性能: valid NDCG@10=0.2964, valid HR@10=0.5186
[Aug-26-2025_23-44-37] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-37] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-43] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-46] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-44-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-46] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-49] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-44-49] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-44-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-49] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-53] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-44-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-44-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-44-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-55] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-44-57] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-44-57] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-44-57] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-45-03] - 传统评估结果 - Epoch 31: NDCG@10=0.2880, HR@10=0.5027
[Aug-26-2025_23-45-03] - epoch:31, time: 22.022021(s), valid (NDCG@10: 0.2880, HR@10: 0.5027), test: SKIPPED, all_time: 781.086159(s)
[Aug-26-2025_23-45-06] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-06] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-06] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-09] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-09] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-45-15] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-45-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-19] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-45-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-45-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-23] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-23] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-26] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-45-28] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-28] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-28] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-45-35] - 传统评估结果 - Epoch 32: NDCG@10=0.2876, HR@10=0.5228
[Aug-26-2025_23-45-35] - epoch:32, time: 24.823147(s), valid (NDCG@10: 0.2876, HR@10: 0.5228), test: SKIPPED, all_time: 805.909307(s)
[Aug-26-2025_23-45-35] - 新的最佳性能: valid NDCG@10=0.2964, valid HR@10=0.5228
[Aug-26-2025_23-45-38] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-38] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-45-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-45-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-48] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-45-52] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-45-52] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-45-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-45-52] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-45-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-45-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-45-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-45-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-46-09] - 传统评估结果 - Epoch 33: NDCG@10=0.2915, HR@10=0.5090
[Aug-26-2025_23-46-09] - epoch:33, time: 27.710421(s), valid (NDCG@10: 0.2915, HR@10: 0.5090), test: SKIPPED, all_time: 833.619727(s)
[Aug-26-2025_23-46-12] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-12] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-15] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-22] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-46-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-25] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-46-25] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-31] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-33] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-33] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-33] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-46-39] - 传统评估结果 - Epoch 34: NDCG@10=0.3049, HR@10=0.5334
[Aug-26-2025_23-46-39] - epoch:34, time: 24.609329(s), valid (NDCG@10: 0.3049, HR@10: 0.5334), test: SKIPPED, all_time: 858.229056(s)
[Aug-26-2025_23-46-39] - 新的最佳性能: valid NDCG@10=0.3049, valid HR@10=0.5334
[Aug-26-2025_23-46-41] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-41] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-45] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-51] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-46-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-46-54] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-46-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-46-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-46-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-46-57] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-46-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-46-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-46-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-47-10] - 传统评估结果 - Epoch 35: NDCG@10=0.2984, HR@10=0.5260
[Aug-26-2025_23-47-10] - epoch:35, time: 24.123941(s), valid (NDCG@10: 0.2984, HR@10: 0.5260), test: SKIPPED, all_time: 882.352997(s)
[Aug-26-2025_23-47-12] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-12] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-22] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-47-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-25] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-47-25] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-26] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-32] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-32] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-33] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-33] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-33] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-47-39] - 传统评估结果 - Epoch 36: NDCG@10=0.2991, HR@10=0.5281
[Aug-26-2025_23-47-39] - epoch:36, time: 23.457371(s), valid (NDCG@10: 0.2991, HR@10: 0.5281), test: SKIPPED, all_time: 905.810368(s)
[Aug-26-2025_23-47-42] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-42] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-45] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-50] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-47-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-47-53] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-47-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-47-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-47-53] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-47-57] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-47-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-47-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-47-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-48-08] - 传统评估结果 - Epoch 37: NDCG@10=0.3076, HR@10=0.5398
[Aug-26-2025_23-48-08] - epoch:37, time: 23.197077(s), valid (NDCG@10: 0.3076, HR@10: 0.5398), test: SKIPPED, all_time: 929.007445(s)
[Aug-26-2025_23-48-08] - 新的最佳性能: valid NDCG@10=0.3076, valid HR@10=0.5398
[Aug-26-2025_23-48-11] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-11] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-14] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-17] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-17] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-20] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-48-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-20] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-23] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-48-23] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-23] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-26] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-29] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-30] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-30] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-30] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-48-34] - 传统评估结果 - Epoch 38: NDCG@10=0.2994, HR@10=0.5355
[Aug-26-2025_23-48-34] - epoch:38, time: 21.999099(s), valid (NDCG@10: 0.2994, HR@10: 0.5355), test: SKIPPED, all_time: 951.006543(s)
[Aug-26-2025_23-48-37] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-37] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-45] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-48-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-48-48] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-48-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-48-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-48-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-48-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-48-55] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-48-55] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-48-55] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-49-00] - 传统评估结果 - Epoch 39: NDCG@10=0.3025, HR@10=0.5270
[Aug-26-2025_23-49-00] - epoch:39, time: 20.738490(s), valid (NDCG@10: 0.3025, HR@10: 0.5270), test: SKIPPED, all_time: 971.745033(s)
[Aug-26-2025_23-49-02] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-02] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-05] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-11] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-49-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-14] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-49-14] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-14] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-21] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-21] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-21] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-49-25] - 传统评估结果 - Epoch 40: NDCG@10=0.3057, HR@10=0.5398
[Aug-26-2025_23-49-25] - epoch:40, time: 21.083533(s), valid (NDCG@10: 0.3057, HR@10: 0.5398), test: SKIPPED, all_time: 992.828566(s)
[Aug-26-2025_23-49-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-49-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-49-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-49-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-49-50] - 传统评估结果 - Epoch 41: NDCG@10=0.3063, HR@10=0.5398
[Aug-26-2025_23-49-50] - epoch:41, time: 20.621657(s), valid (NDCG@10: 0.3063, HR@10: 0.5398), test: SKIPPED, all_time: 1013.450223(s)
[Aug-26-2025_23-49-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-49-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-49-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-49-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-49-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-49-58] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-49-58] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-01] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-04] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-04] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-11] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-11] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-11] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-50-16] - 传统评估结果 - Epoch 42: NDCG@10=0.3107, HR@10=0.5504
[Aug-26-2025_23-50-16] - epoch:42, time: 20.899340(s), valid (NDCG@10: 0.3107, HR@10: 0.5504), test: SKIPPED, all_time: 1034.349563(s)
[Aug-26-2025_23-50-16] - 新的最佳性能: valid NDCG@10=0.3107, valid HR@10=0.5504
[Aug-26-2025_23-50-18] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-23] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-23] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-26] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-26] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-29] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-29] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-29] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-36] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-36] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-36] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-50-40] - 传统评估结果 - Epoch 43: NDCG@10=0.3139, HR@10=0.5451
[Aug-26-2025_23-50-40] - epoch:43, time: 20.290601(s), valid (NDCG@10: 0.3139, HR@10: 0.5451), test: SKIPPED, all_time: 1054.640163(s)
[Aug-26-2025_23-50-43] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-45] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-51] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-54] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-50-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-50-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-50-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-50-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-50-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-50-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-50-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-50-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-01] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-01] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-01] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-01] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-51-06] - 传统评估结果 - Epoch 44: NDCG@10=0.3065, HR@10=0.5451
[Aug-26-2025_23-51-06] - epoch:44, time: 20.605957(s), valid (NDCG@10: 0.3065, HR@10: 0.5451), test: SKIPPED, all_time: 1075.246120(s)
[Aug-26-2025_23-51-08] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-08] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-14] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-16] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-51-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-19] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-51-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-26] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-26] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-26] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-26] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-51-30] - 传统评估结果 - Epoch 45: NDCG@10=0.3170, HR@10=0.5546
[Aug-26-2025_23-51-30] - epoch:45, time: 20.508550(s), valid (NDCG@10: 0.3170, HR@10: 0.5546), test: SKIPPED, all_time: 1095.754671(s)
[Aug-26-2025_23-51-30] - 新的最佳性能: valid NDCG@10=0.3170, valid HR@10=0.5546
[Aug-26-2025_23-51-33] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-33] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-33] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-42] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-51-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-51-44] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-51-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-51-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-51-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-51-51] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-51] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-51-51] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-51-57] - 传统评估结果 - Epoch 46: NDCG@10=0.3159, HR@10=0.5578
[Aug-26-2025_23-51-57] - epoch:46, time: 20.900749(s), valid (NDCG@10: 0.3159, HR@10: 0.5578), test: SKIPPED, all_time: 1116.655420(s)
[Aug-26-2025_23-51-57] - 新的最佳性能: valid NDCG@10=0.3170, valid HR@10=0.5578
[Aug-26-2025_23-51-59] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-59] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-51-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-51-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-02] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-05] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-08] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-52-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-11] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-52-11] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-15] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-20] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-20] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-20] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-52-25] - 传统评估结果 - Epoch 47: NDCG@10=0.3223, HR@10=0.5652
[Aug-26-2025_23-52-25] - epoch:47, time: 23.641281(s), valid (NDCG@10: 0.3223, HR@10: 0.5652), test: SKIPPED, all_time: 1140.296701(s)
[Aug-26-2025_23-52-25] - 新的最佳性能: valid NDCG@10=0.3223, valid HR@10=0.5652
[Aug-26-2025_23-52-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-52-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-52-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-52-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-52-50] - 传统评估结果 - Epoch 48: NDCG@10=0.3235, HR@10=0.5620
[Aug-26-2025_23-52-50] - epoch:48, time: 20.676061(s), valid (NDCG@10: 0.3235, HR@10: 0.5620), test: SKIPPED, all_time: 1160.972762(s)
[Aug-26-2025_23-52-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-52-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-52-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-52-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-52-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-52-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-52-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-04] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-05] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-05] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-08] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-12] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-12] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-12] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-53-16] - 传统评估结果 - Epoch 49: NDCG@10=0.3253, HR@10=0.5610
[Aug-26-2025_23-53-16] - epoch:49, time: 21.319600(s), valid (NDCG@10: 0.3253, HR@10: 0.5610), test: SKIPPED, all_time: 1182.292362(s)
[Aug-26-2025_23-53-19] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-27] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-30] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-30] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-30] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-33] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-36] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-37] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-37] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-37] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-53-42] - 传统评估结果 - Epoch 50: NDCG@10=0.3327, HR@10=0.5652
[Aug-26-2025_23-53-42] - epoch:50, time: 20.857728(s), valid (NDCG@10: 0.3327, HR@10: 0.5652), test: SKIPPED, all_time: 1203.150090(s)
[Aug-26-2025_23-53-44] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-44] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-52] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-53-55] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-53-55] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-53-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-53-55] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-53-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-53-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-53-58] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-53-58] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-54-07] - 传统评估结果 - Epoch 51: NDCG@10=0.3264, HR@10=0.5567
[Aug-26-2025_23-54-07] - epoch:51, time: 20.671510(s), valid (NDCG@10: 0.3264, HR@10: 0.5567), test: SKIPPED, all_time: 1223.821600(s)
[Aug-26-2025_23-54-10] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-15] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-54-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-21] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-54-21] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-28] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-28] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-28] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-54-33] - 传统评估结果 - Epoch 52: NDCG@10=0.3336, HR@10=0.5779
[Aug-26-2025_23-54-33] - epoch:52, time: 20.836937(s), valid (NDCG@10: 0.3336, HR@10: 0.5779), test: SKIPPED, all_time: 1244.658537(s)
[Aug-26-2025_23-54-33] - 新的最佳性能: valid NDCG@10=0.3336, valid HR@10=0.5779
[Aug-26-2025_23-54-36] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-36] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-39] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-45] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-54-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-54-48] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-54-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-54-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-54-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-54-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-54-55] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-54-55] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-54-55] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-55-01] - 传统评估结果 - Epoch 53: NDCG@10=0.3337, HR@10=0.5790
[Aug-26-2025_23-55-01] - epoch:53, time: 22.515724(s), valid (NDCG@10: 0.3337, HR@10: 0.5790), test: SKIPPED, all_time: 1267.174261(s)
[Aug-26-2025_23-55-01] - 新的最佳性能: valid NDCG@10=0.3337, valid HR@10=0.5790
[Aug-26-2025_23-55-04] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-04] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-04] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-55-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-16] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-55-16] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-19] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-22] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-23] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-23] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-23] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-23] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-55-28] - 传统评估结果 - Epoch 54: NDCG@10=0.3328, HR@10=0.5801
[Aug-26-2025_23-55-28] - epoch:54, time: 22.382318(s), valid (NDCG@10: 0.3328, HR@10: 0.5801), test: SKIPPED, all_time: 1289.556579(s)
[Aug-26-2025_23-55-28] - 新的最佳性能: valid NDCG@10=0.3337, valid HR@10=0.5801
[Aug-26-2025_23-55-31] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-31] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-34] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-40] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-55-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-42] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-55-42] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-43] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-45] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-55-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-55-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-55-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-55-49] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-49] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-49] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-55-49] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-55-55] - 传统评估结果 - Epoch 55: NDCG@10=0.3318, HR@10=0.5854
[Aug-26-2025_23-55-55] - epoch:55, time: 21.259960(s), valid (NDCG@10: 0.3318, HR@10: 0.5854), test: SKIPPED, all_time: 1310.816540(s)
[Aug-26-2025_23-55-55] - 新的最佳性能: valid NDCG@10=0.3337, valid HR@10=0.5854
[Aug-26-2025_23-55-57] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-57] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-55-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-55-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-01] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-07] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-56-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-56-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-18] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-18] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-18] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-56-23] - 传统评估结果 - Epoch 56: NDCG@10=0.3288, HR@10=0.5716
[Aug-26-2025_23-56-23] - epoch:56, time: 23.131213(s), valid (NDCG@10: 0.3288, HR@10: 0.5716), test: SKIPPED, all_time: 1333.947753(s)
[Aug-26-2025_23-56-25] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-25] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-31] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-34] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-56-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-56-37] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-43] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-56-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-56-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-43] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-56-44] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-44] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-56-44] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-56-49] - 传统评估结果 - Epoch 57: NDCG@10=0.3281, HR@10=0.5822
[Aug-26-2025_23-56-49] - epoch:57, time: 21.637672(s), valid (NDCG@10: 0.3281, HR@10: 0.5822), test: SKIPPED, all_time: 1355.585425(s)
[Aug-26-2025_23-56-51] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-51] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-56-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-56-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-56-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-08] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-57-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-11] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-57-11] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-11] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-14] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-17] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-17] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-19] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-19] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-19] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-19] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-57-24] - 传统评估结果 - Epoch 58: NDCG@10=0.3433, HR@10=0.5801
[Aug-26-2025_23-57-24] - epoch:58, time: 29.963201(s), valid (NDCG@10: 0.3433, HR@10: 0.5801), test: SKIPPED, all_time: 1385.548626(s)
[Aug-26-2025_23-57-27] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-57-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-57-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-57-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-57-51] - 传统评估结果 - Epoch 59: NDCG@10=0.3356, HR@10=0.5748
[Aug-26-2025_23-57-51] - epoch:59, time: 21.642282(s), valid (NDCG@10: 0.3356, HR@10: 0.5748), test: SKIPPED, all_time: 1407.190908(s)
[Aug-26-2025_23-57-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-57-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-57-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-57-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-57-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-57-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-57-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-58-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-05] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-58-05] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-09] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-09] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-13] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-13] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-13] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-58-19] - 传统评估结果 - Epoch 60: NDCG@10=0.3378, HR@10=0.5864
[Aug-26-2025_23-58-19] - epoch:60, time: 22.631262(s), valid (NDCG@10: 0.3378, HR@10: 0.5864), test: SKIPPED, all_time: 1429.822170(s)
[Aug-26-2025_23-58-19] - 新的最佳性能: valid NDCG@10=0.3433, valid HR@10=0.5864
[Aug-26-2025_23-58-22] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-22] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-29] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-29] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-29] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-32] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-58-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-32] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-58-36] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-36] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-39] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-43] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-43] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-58-44] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-44] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-44] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-58-50] - 传统评估结果 - Epoch 61: NDCG@10=0.3387, HR@10=0.5832
[Aug-26-2025_23-58-50] - epoch:61, time: 25.279170(s), valid (NDCG@10: 0.3387, HR@10: 0.5832), test: SKIPPED, all_time: 1455.101340(s)
[Aug-26-2025_23-58-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-58-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-58-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-58-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-58-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-58-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-58-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-03] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-59-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-07] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-59-07] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-07] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-15] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-16] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-16] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-16] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-59-21] - 传统评估结果 - Epoch 62: NDCG@10=0.3434, HR@10=0.6055
[Aug-26-2025_23-59-21] - epoch:62, time: 26.611503(s), valid (NDCG@10: 0.3434, HR@10: 0.6055), test: SKIPPED, all_time: 1481.712843(s)
[Aug-26-2025_23-59-21] - 新的最佳性能: valid NDCG@10=0.3434, valid HR@10=0.6055
[Aug-26-2025_23-59-24] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-24] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-27] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-30] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-33] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-59-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-34] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-37] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_23-59-37] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-37] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-26-2025_23-59-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-26-2025_23-59-52] - 传统评估结果 - Epoch 63: NDCG@10=0.3434, HR@10=0.6002
[Aug-26-2025_23-59-52] - epoch:63, time: 24.711638(s), valid (NDCG@10: 0.3434, HR@10: 0.6002), test: SKIPPED, all_time: 1506.424481(s)
[Aug-26-2025_23-59-54] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_23-59-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_23-59-54] - 梯度聚类完成: 128 -> 10个客户端
[Aug-26-2025_23-59-57] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_23-59-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_23-59-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_23-59-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-04] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-00-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-04] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-08] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-00-08] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-18] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-18] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-18] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-00-24] - 传统评估结果 - Epoch 64: NDCG@10=0.3457, HR@10=0.5896
[Aug-27-2025_00-00-24] - epoch:64, time: 26.224351(s), valid (NDCG@10: 0.3457, HR@10: 0.5896), test: SKIPPED, all_time: 1532.648832(s)
[Aug-27-2025_00-00-27] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-00-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-00-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-43] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-00-47] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-47] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-47] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-00-53] - 传统评估结果 - Epoch 65: NDCG@10=0.3498, HR@10=0.5928
[Aug-27-2025_00-00-53] - epoch:65, time: 22.999986(s), valid (NDCG@10: 0.3498, HR@10: 0.5928), test: SKIPPED, all_time: 1555.648818(s)
[Aug-27-2025_00-00-55] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-55] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-00-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-00-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-00-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-00-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-00-59] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-00-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-02] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-02] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-05] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-01-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-09] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-01-09] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-09] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-15] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-17] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-17] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-17] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-01-22] - 传统评估结果 - Epoch 66: NDCG@10=0.3417, HR@10=0.5949
[Aug-27-2025_00-01-22] - epoch:66, time: 23.890495(s), valid (NDCG@10: 0.3417, HR@10: 0.5949), test: SKIPPED, all_time: 1579.539312(s)
[Aug-27-2025_00-01-25] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-25] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-31] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-34] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-01-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-35] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-38] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-01-38] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-38] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-40] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-43] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-43] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-01-44] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-45] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-45] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-45] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-01-50] - 传统评估结果 - Epoch 67: NDCG@10=0.3538, HR@10=0.6076
[Aug-27-2025_00-01-50] - epoch:67, time: 22.485205(s), valid (NDCG@10: 0.3538, HR@10: 0.6076), test: SKIPPED, all_time: 1602.024517(s)
[Aug-27-2025_00-01-50] - 新的最佳性能: valid NDCG@10=0.3538, valid HR@10=0.6076
[Aug-27-2025_00-01-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-01-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-01-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-01-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-01-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-01-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-01-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-02-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-05] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-02-05] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-09] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-09] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-14] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-14] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-14] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-02-19] - 传统评估结果 - Epoch 68: NDCG@10=0.3514, HR@10=0.5970
[Aug-27-2025_00-02-19] - epoch:68, time: 23.495975(s), valid (NDCG@10: 0.3514, HR@10: 0.5970), test: SKIPPED, all_time: 1625.520492(s)
[Aug-27-2025_00-02-22] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-22] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-30] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-02-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-33] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-02-33] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-41] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-41] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-41] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-02-46] - 传统评估结果 - Epoch 69: NDCG@10=0.3592, HR@10=0.6076
[Aug-27-2025_00-02-46] - epoch:69, time: 22.091639(s), valid (NDCG@10: 0.3592, HR@10: 0.6076), test: SKIPPED, all_time: 1647.612131(s)
[Aug-27-2025_00-02-49] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-49] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-02-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-49] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-52] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-52] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-02-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-02-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-02-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-55] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-02-59] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-02-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-02-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-02-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-03-03] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-03] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-03-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-06] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-06] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-09] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-09] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-03-11] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-11] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-11] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-03-16] - 传统评估结果 - Epoch 70: NDCG@10=0.3454, HR@10=0.5960
[Aug-27-2025_00-03-16] - epoch:70, time: 24.788663(s), valid (NDCG@10: 0.3454, HR@10: 0.5960), test: SKIPPED, all_time: 1672.400794(s)
[Aug-27-2025_00-03-19] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-03-31] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-03-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-35] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-03-35] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-03-38] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-38] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-03-43] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-43] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-43] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-43] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-03-49] - 传统评估结果 - Epoch 71: NDCG@10=0.3533, HR@10=0.5992
[Aug-27-2025_00-03-49] - epoch:71, time: 27.124535(s), valid (NDCG@10: 0.3533, HR@10: 0.5992), test: SKIPPED, all_time: 1699.525328(s)
[Aug-27-2025_00-03-52] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-52] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-03-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-03-54] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-03-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-03-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-03-58] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-03-58] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-01] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-04-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-04] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-04-04] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-08] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-11] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-12] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-13] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-13] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-04-18] - 传统评估结果 - Epoch 72: NDCG@10=0.3494, HR@10=0.6034
[Aug-27-2025_00-04-18] - epoch:72, time: 23.920963(s), valid (NDCG@10: 0.3494, HR@10: 0.6034), test: SKIPPED, all_time: 1723.446291(s)
[Aug-27-2025_00-04-21] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-21] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-29] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-04-29] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-32] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-04-32] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-32] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-32] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-35] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-38] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-40] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-40] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-40] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-04-45] - 传统评估结果 - Epoch 73: NDCG@10=0.3555, HR@10=0.6055
[Aug-27-2025_00-04-45] - epoch:73, time: 21.712812(s), valid (NDCG@10: 0.3555, HR@10: 0.6055), test: SKIPPED, all_time: 1745.159103(s)
[Aug-27-2025_00-04-47] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-04-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-48] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-04-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-04-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-04-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-04-57] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-04-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-04-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-04-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-01] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-05-01] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-05-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-05] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-05-10] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-10] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-10] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-10] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-05-16] - 传统评估结果 - Epoch 74: NDCG@10=0.3600, HR@10=0.6013
[Aug-27-2025_00-05-16] - epoch:74, time: 25.327169(s), valid (NDCG@10: 0.3600, HR@10: 0.6013), test: SKIPPED, all_time: 1770.486272(s)
[Aug-27-2025_00-05-19] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-20] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-23] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-23] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-23] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-26] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-05-30] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-05-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-33] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-05-33] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-05-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-05-42] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-42] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-42] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-05-48] - 传统评估结果 - Epoch 75: NDCG@10=0.3501, HR@10=0.5970
[Aug-27-2025_00-05-48] - epoch:75, time: 25.866506(s), valid (NDCG@10: 0.3501, HR@10: 0.5970), test: SKIPPED, all_time: 1796.352778(s)
[Aug-27-2025_00-05-51] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-51] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-05-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-05-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-05-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-05-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-05-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-05-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-06-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-06] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-06-06] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-10] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-15] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-15] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-15] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-15] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-06-21] - 传统评估结果 - Epoch 76: NDCG@10=0.3493, HR@10=0.6002
[Aug-27-2025_00-06-21] - epoch:76, time: 27.071427(s), valid (NDCG@10: 0.3493, HR@10: 0.6002), test: SKIPPED, all_time: 1823.424206(s)
[Aug-27-2025_00-06-24] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-24] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-31] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-35] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-06-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-35] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-38] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-06-38] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-38] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-06-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-06-52] - 传统评估结果 - Epoch 77: NDCG@10=0.3589, HR@10=0.6087
[Aug-27-2025_00-06-52] - epoch:77, time: 25.154596(s), valid (NDCG@10: 0.3589, HR@10: 0.6087), test: SKIPPED, all_time: 1848.578802(s)
[Aug-27-2025_00-06-52] - 新的最佳性能: valid NDCG@10=0.3600, valid HR@10=0.6087
[Aug-27-2025_00-06-55] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-55] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-06-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-06-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-06-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-06-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-06-59] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-06-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-02] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-03] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-06] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-07-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-06] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-07-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-07-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-17] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-17] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-18] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-18] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-19] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-07-25] - 传统评估结果 - Epoch 78: NDCG@10=0.3552, HR@10=0.6140
[Aug-27-2025_00-07-25] - epoch:78, time: 26.391695(s), valid (NDCG@10: 0.3552, HR@10: 0.6140), test: SKIPPED, all_time: 1874.970497(s)
[Aug-27-2025_00-07-25] - 新的最佳性能: valid NDCG@10=0.3600, valid HR@10=0.6140
[Aug-27-2025_00-07-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-07-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-07-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-35] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-07-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-42] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-07-42] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-07-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-46] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-07-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-07-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-07-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-07-51] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-07-51] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-07-51] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-07-57] - 传统评估结果 - Epoch 79: NDCG@10=0.3628, HR@10=0.6129
[Aug-27-2025_00-07-57] - epoch:79, time: 26.337460(s), valid (NDCG@10: 0.3628, HR@10: 0.6129), test: SKIPPED, all_time: 1901.307957(s)
[Aug-27-2025_00-07-59] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-00] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-02] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-03] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-09] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-08-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-08-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-20] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-20] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-21] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-08-26] - 传统评估结果 - Epoch 80: NDCG@10=0.3602, HR@10=0.6066
[Aug-27-2025_00-08-26] - epoch:80, time: 24.029293(s), valid (NDCG@10: 0.3602, HR@10: 0.6066), test: SKIPPED, all_time: 1925.337250(s)
[Aug-27-2025_00-08-29] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-29] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-29] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-35] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-38] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-08-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-08-41] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-08-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-08-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-08-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-08-49] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-49] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-49] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-08-49] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-08-55] - 传统评估结果 - Epoch 81: NDCG@10=0.3625, HR@10=0.6055
[Aug-27-2025_00-08-55] - epoch:81, time: 23.506575(s), valid (NDCG@10: 0.3625, HR@10: 0.6055), test: SKIPPED, all_time: 1948.843825(s)
[Aug-27-2025_00-08-57] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-57] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-08-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-08-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-07] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-09-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-09-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-17] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-18] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-18] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-18] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-09-23] - 传统评估结果 - Epoch 82: NDCG@10=0.3551, HR@10=0.6002
[Aug-27-2025_00-09-23] - epoch:82, time: 23.327844(s), valid (NDCG@10: 0.3551, HR@10: 0.6002), test: SKIPPED, all_time: 1972.171669(s)
[Aug-27-2025_00-09-26] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-26] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-26] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-29] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-29] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-29] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-29] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-32] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-32] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-35] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-09-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-35] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-38] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-09-38] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-38] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-41] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-09-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-09-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-09-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-09-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-09-53] - 传统评估结果 - Epoch 83: NDCG@10=0.3567, HR@10=0.6172
[Aug-27-2025_00-09-53] - epoch:83, time: 23.016251(s), valid (NDCG@10: 0.3567, HR@10: 0.6172), test: SKIPPED, all_time: 1995.187920(s)
[Aug-27-2025_00-09-53] - 新的最佳性能: valid NDCG@10=0.3628, valid HR@10=0.6172
[Aug-27-2025_00-09-56] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-56] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-09-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-09-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-09-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-08] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-10-09] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-12] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-10-12] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-12] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-12] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-15] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-19] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-19] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-20] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-10-24] - 传统评估结果 - Epoch 84: NDCG@10=0.3644, HR@10=0.6119
[Aug-27-2025_00-10-24] - epoch:84, time: 26.946406(s), valid (NDCG@10: 0.3644, HR@10: 0.6119), test: SKIPPED, all_time: 2022.134326(s)
[Aug-27-2025_00-10-27] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-10-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-10-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-10-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-10-52] - 传统评估结果 - Epoch 85: NDCG@10=0.3601, HR@10=0.6098
[Aug-27-2025_00-10-52] - epoch:85, time: 22.173752(s), valid (NDCG@10: 0.3601, HR@10: 0.6098), test: SKIPPED, all_time: 2044.308078(s)
[Aug-27-2025_00-10-55] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-55] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-10-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-10-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-10-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-10-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-10-59] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-10-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-03] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-07] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-11-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-11-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-11-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-11] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-14] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-18] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-20] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-20] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-20] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-11-25] - 传统评估结果 - Epoch 86: NDCG@10=0.3689, HR@10=0.6235
[Aug-27-2025_00-11-25] - epoch:86, time: 27.844822(s), valid (NDCG@10: 0.3689, HR@10: 0.6235), test: SKIPPED, all_time: 2072.152900(s)
[Aug-27-2025_00-11-25] - 新的最佳性能: valid NDCG@10=0.3689, valid HR@10=0.6235
[Aug-27-2025_00-11-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-11-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-11-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-38] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-11-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-38] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-11-41] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-11-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-11-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-11-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-11-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-11-48] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-11-48] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-48] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-11-48] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-11-54] - 传统评估结果 - Epoch 87: NDCG@10=0.3561, HR@10=0.6172
[Aug-27-2025_00-11-54] - epoch:87, time: 23.114576(s), valid (NDCG@10: 0.3561, HR@10: 0.6172), test: SKIPPED, all_time: 2095.267476(s)
[Aug-27-2025_00-11-58] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-00] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-00] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-04] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-11] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-12-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-14] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-12-14] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-14] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-20] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-21] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-21] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-21] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-12-26] - 传统评估结果 - Epoch 88: NDCG@10=0.3740, HR@10=0.6214
[Aug-27-2025_00-12-26] - epoch:88, time: 27.594975(s), valid (NDCG@10: 0.3740, HR@10: 0.6214), test: SKIPPED, all_time: 2122.862450(s)
[Aug-27-2025_00-12-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-34] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-12-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-12-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-12-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-12-51] - 传统评估结果 - Epoch 89: NDCG@10=0.3624, HR@10=0.6108
[Aug-27-2025_00-12-51] - epoch:89, time: 20.766589(s), valid (NDCG@10: 0.3624, HR@10: 0.6108), test: SKIPPED, all_time: 2143.629040(s)
[Aug-27-2025_00-12-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-12-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-12-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-12-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-12-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-12-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-12-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-05] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-05] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-05] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-08] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-08] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-11] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-12] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-12] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-12] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-13-17] - 传统评估结果 - Epoch 90: NDCG@10=0.3642, HR@10=0.6193
[Aug-27-2025_00-13-17] - epoch:90, time: 21.284730(s), valid (NDCG@10: 0.3642, HR@10: 0.6193), test: SKIPPED, all_time: 2164.913770(s)
[Aug-27-2025_00-13-19] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-19] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-22] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-25] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-27] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-30] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-30] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-30] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-33] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-33] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-36] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-37] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-37] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-37] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-13-42] - 传统评估结果 - Epoch 91: NDCG@10=0.3628, HR@10=0.6204
[Aug-27-2025_00-13-42] - epoch:91, time: 20.889844(s), valid (NDCG@10: 0.3628, HR@10: 0.6204), test: SKIPPED, all_time: 2185.803614(s)
[Aug-27-2025_00-13-45] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-45] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-47] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-50] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-53] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-13-56] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-13-56] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-13-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-13-56] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-13-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-13-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-13-59] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-13-59] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-02] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-03] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-03] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-03] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-03] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-14-08] - 传统评估结果 - Epoch 92: NDCG@10=0.3589, HR@10=0.6172
[Aug-27-2025_00-14-08] - epoch:92, time: 20.813724(s), valid (NDCG@10: 0.3589, HR@10: 0.6172), test: SKIPPED, all_time: 2206.617339(s)
[Aug-27-2025_00-14-10] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-19] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-14-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-21] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-14-22] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-22] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-28] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-28] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-28] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-14-33] - 传统评估结果 - Epoch 93: NDCG@10=0.3712, HR@10=0.6267
[Aug-27-2025_00-14-33] - epoch:93, time: 20.898984(s), valid (NDCG@10: 0.3712, HR@10: 0.6267), test: SKIPPED, all_time: 2227.516323(s)
[Aug-27-2025_00-14-33] - 新的最佳性能: valid NDCG@10=0.3740, valid HR@10=0.6267
[Aug-27-2025_00-14-35] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-35] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-38] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-38] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-14-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-47] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-14-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-14-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-49] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-49] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-14-52] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-14-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-14-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-52] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-14-54] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-14-54] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-14-54] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-14-58] - 传统评估结果 - Epoch 94: NDCG@10=0.3655, HR@10=0.6151
[Aug-27-2025_00-14-58] - epoch:94, time: 20.814008(s), valid (NDCG@10: 0.3655, HR@10: 0.6151), test: SKIPPED, all_time: 2248.330331(s)
[Aug-27-2025_00-15-01] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-01] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-04] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-04] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-07] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-15-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-15-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-20] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-20] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-20] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-15-25] - 传统评估结果 - Epoch 95: NDCG@10=0.3698, HR@10=0.6214
[Aug-27-2025_00-15-25] - epoch:95, time: 21.704289(s), valid (NDCG@10: 0.3698, HR@10: 0.6214), test: SKIPPED, all_time: 2270.034621(s)
[Aug-27-2025_00-15-27] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-15-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-15-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-45] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-15-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-15-51] - 传统评估结果 - Epoch 96: NDCG@10=0.3609, HR@10=0.6182
[Aug-27-2025_00-15-51] - epoch:96, time: 21.126171(s), valid (NDCG@10: 0.3609, HR@10: 0.6182), test: SKIPPED, all_time: 2291.160792(s)
[Aug-27-2025_00-15-54] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-15-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-15-54] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-15-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-15-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-15-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-15-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-03] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-16-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-06] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-16-06] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-10] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-14] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-14] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-14] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-16-19] - 传统评估结果 - Epoch 97: NDCG@10=0.3537, HR@10=0.6204
[Aug-27-2025_00-16-19] - epoch:97, time: 22.936188(s), valid (NDCG@10: 0.3537, HR@10: 0.6204), test: SKIPPED, all_time: 2314.096980(s)
[Aug-27-2025_00-16-22] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-22] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-22] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-28] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-30] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-16-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-30] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-33] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-16-33] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-36] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-40] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-40] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-40] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-40] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-16-45] - 传统评估结果 - Epoch 98: NDCG@10=0.3673, HR@10=0.6278
[Aug-27-2025_00-16-45] - epoch:98, time: 21.043966(s), valid (NDCG@10: 0.3673, HR@10: 0.6278), test: SKIPPED, all_time: 2335.140946(s)
[Aug-27-2025_00-16-45] - 新的最佳性能: valid NDCG@10=0.3740, valid HR@10=0.6278
[Aug-27-2025_00-16-48] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-48] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-16-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-48] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-51] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-16-51] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-16-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-16-56] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-16-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-16-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-16-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-16-59] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-17-00] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-00] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-03] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-03] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-03] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-06] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-06] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-07] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-07] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-07] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-17-13] - 传统评估结果 - Epoch 99: NDCG@10=0.3592, HR@10=0.6151
[Aug-27-2025_00-17-13] - epoch:99, time: 22.376259(s), valid (NDCG@10: 0.3592, HR@10: 0.6151), test: SKIPPED, all_time: 2357.517206(s)
[Aug-27-2025_00-17-15] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-15] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-15] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-18] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-18] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-24] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-17-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-27] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-17-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-34] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-34] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-34] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-34] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-17-39] - 传统评估结果 - Epoch 100: NDCG@10=0.3612, HR@10=0.6140
[Aug-27-2025_00-17-39] - epoch:100, time: 21.341389(s), valid (NDCG@10: 0.3612, HR@10: 0.6140), test: SKIPPED, all_time: 2378.858594(s)
[Aug-27-2025_00-17-42] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-42] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-44] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-47] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-47] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-50] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-17-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-50] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-50] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-53] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-17-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-17-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-53] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-17-56] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-56] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-17-56] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-17-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-17-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-17-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-17-59] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-00] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-00] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-00] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-00] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-18-06] - 传统评估结果 - Epoch 101: NDCG@10=0.3646, HR@10=0.6299
[Aug-27-2025_00-18-06] - epoch:101, time: 21.530937(s), valid (NDCG@10: 0.3646, HR@10: 0.6299), test: SKIPPED, all_time: 2400.389531(s)
[Aug-27-2025_00-18-06] - 新的最佳性能: valid NDCG@10=0.3740, valid HR@10=0.6299
[Aug-27-2025_00-18-09] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-09] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-09] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-09] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-15] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-18-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-21] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-18-21] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-26] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-28] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-28] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-28] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-18-33] - 传统评估结果 - Epoch 102: NDCG@10=0.3642, HR@10=0.6193
[Aug-27-2025_00-18-33] - epoch:102, time: 21.955283(s), valid (NDCG@10: 0.3642, HR@10: 0.6193), test: SKIPPED, all_time: 2422.344814(s)
[Aug-27-2025_00-18-36] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-36] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-38] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-38] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-38] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-38] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-41] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-41] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-41] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-41] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-44] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-18-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-44] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-47] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-18-47] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-18-47] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-47] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-50] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-50] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-18-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-18-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-18-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-53] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-18-54] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-18-54] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-18-54] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-18-59] - 传统评估结果 - Epoch 103: NDCG@10=0.3597, HR@10=0.6214
[Aug-27-2025_00-18-59] - epoch:103, time: 21.558679(s), valid (NDCG@10: 0.3597, HR@10: 0.6214), test: SKIPPED, all_time: 2443.903494(s)
[Aug-27-2025_00-19-02] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-02] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-05] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-11] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-19-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-11] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-14] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-19-14] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-14] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-17] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-17] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-20] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-21] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-21] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-21] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-19-26] - 传统评估结果 - Epoch 104: NDCG@10=0.3571, HR@10=0.6076
[Aug-27-2025_00-19-26] - epoch:104, time: 22.169461(s), valid (NDCG@10: 0.3571, HR@10: 0.6076), test: SKIPPED, all_time: 2466.072954(s)
[Aug-27-2025_00-19-28] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-29] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-34] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-37] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-19-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-37] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-40] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-19-40] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-43] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-43] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-43] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-46] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-46] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-46] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-19-47] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-47] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-47] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-47] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-19-52] - 传统评估结果 - Epoch 105: NDCG@10=0.3584, HR@10=0.6225
[Aug-27-2025_00-19-52] - epoch:105, time: 21.393055(s), valid (NDCG@10: 0.3584, HR@10: 0.6225), test: SKIPPED, all_time: 2487.466009(s)
[Aug-27-2025_00-19-55] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-55] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-19-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-19-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-19-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-19-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-19-58] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-19-58] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-02] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-02] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-05] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-20-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-05] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-08] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-20-08] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-11] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-11] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-11] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-14] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-15] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-15] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-15] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-15] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-20-20] - 传统评估结果 - Epoch 106: NDCG@10=0.3557, HR@10=0.6098
[Aug-27-2025_00-20-20] - epoch:106, time: 22.979870(s), valid (NDCG@10: 0.3557, HR@10: 0.6098), test: SKIPPED, all_time: 2510.445879(s)
[Aug-27-2025_00-20-22] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-23] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-23] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-26] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-28] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-31] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-20-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-34] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-20-34] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-34] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-37] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-37] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-37] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-37] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-40] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-40] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-40] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-40] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-41] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-41] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-41] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-41] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-20-46] - 传统评估结果 - Epoch 107: NDCG@10=0.3591, HR@10=0.6140
[Aug-27-2025_00-20-46] - epoch:107, time: 21.175678(s), valid (NDCG@10: 0.3591, HR@10: 0.6140), test: SKIPPED, all_time: 2531.621556(s)
[Aug-27-2025_00-20-49] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-49] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-20-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-49] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-52] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-52] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-20-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-20-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-20-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-20-57] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-20-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-20-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-20-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-00] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-21-00] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-04] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-04] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-04] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-07] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-08] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-08] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-08] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-08] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-21-14] - 传统评估结果 - Epoch 108: NDCG@10=0.3615, HR@10=0.6246
[Aug-27-2025_00-21-14] - epoch:108, time: 22.384831(s), valid (NDCG@10: 0.3615, HR@10: 0.6246), test: SKIPPED, all_time: 2554.006387(s)
[Aug-27-2025_00-21-17] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-17] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-17] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-17] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-20] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-22] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-22] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-22] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-25] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-21-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-28] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-21-28] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-28] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-31] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-31] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-34] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-34] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-34] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-35] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-35] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-35] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-35] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-21-40] - 传统评估结果 - Epoch 109: NDCG@10=0.3586, HR@10=0.6002
[Aug-27-2025_00-21-40] - epoch:109, time: 21.423717(s), valid (NDCG@10: 0.3586, HR@10: 0.6002), test: SKIPPED, all_time: 2575.430105(s)
[Aug-27-2025_00-21-43] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-46] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-48] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-49] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-51] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-21-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-21-54] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-21-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-21-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-21-54] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-21-57] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-21-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-21-57] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-21-57] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-00] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-22-07] - 传统评估结果 - Epoch 110: NDCG@10=0.3613, HR@10=0.6172
[Aug-27-2025_00-22-07] - epoch:110, time: 21.671574(s), valid (NDCG@10: 0.3613, HR@10: 0.6172), test: SKIPPED, all_time: 2597.101679(s)
[Aug-27-2025_00-22-10] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-13] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-13] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-13] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-13] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-16] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-16] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-19] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-22-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-19] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-21] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-22-22] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-22] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-22] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-25] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-25] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-27] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-29] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-29] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-29] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-29] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-22-34] - 传统评估结果 - Epoch 111: NDCG@10=0.3670, HR@10=0.6225
[Aug-27-2025_00-22-34] - epoch:111, time: 21.725425(s), valid (NDCG@10: 0.3670, HR@10: 0.6225), test: SKIPPED, all_time: 2618.827104(s)
[Aug-27-2025_00-22-36] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-36] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-39] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-39] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-39] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-39] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-42] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-42] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-45] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-22-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-45] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-22-48] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-22-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-48] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-50] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-50] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-50] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-50] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-22-53] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-22-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-22-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-53] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-22-54] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-22-54] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-54] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-22-55] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-22-59] - 传统评估结果 - Epoch 112: NDCG@10=0.3628, HR@10=0.6235
[Aug-27-2025_00-22-59] - epoch:112, time: 20.883758(s), valid (NDCG@10: 0.3628, HR@10: 0.6235), test: SKIPPED, all_time: 2639.710862(s)
[Aug-27-2025_00-23-02] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-02] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-02] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-02] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-05] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-05] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-05] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-05] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-08] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-08] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-08] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-08] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-23-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-13] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-23-13] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-13] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-13] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-16] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-16] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-16] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-16] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-19] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-19] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-20] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-20] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-20] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-20] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-23-25] - 传统评估结果 - Epoch 113: NDCG@10=0.3608, HR@10=0.6214
[Aug-27-2025_00-23-25] - epoch:113, time: 20.987376(s), valid (NDCG@10: 0.3608, HR@10: 0.6214), test: SKIPPED, all_time: 2660.698238(s)
[Aug-27-2025_00-23-27] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-27] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-28] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-30] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-30] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-30] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-30] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-33] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-33] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-33] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-23-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-36] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-36] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-39] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-23-39] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-39] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-39] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-42] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-42] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-42] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-42] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-44] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-44] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-44] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-44] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-23-46] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-46] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-46] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-23-50] - 传统评估结果 - Epoch 114: NDCG@10=0.3654, HR@10=0.6193
[Aug-27-2025_00-23-50] - epoch:114, time: 20.756174(s), valid (NDCG@10: 0.3654, HR@10: 0.6193), test: SKIPPED, all_time: 2681.454412(s)
[Aug-27-2025_00-23-53] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-53] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-23-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-53] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-55] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-55] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-55] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-23-55] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-23-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-23-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-23-58] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-23-58] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-01] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-01] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-04] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-04] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-04] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-07] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-07] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-10] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-10] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-11] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-11] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-11] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-11] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-24-16] - 传统评估结果 - Epoch 115: NDCG@10=0.3601, HR@10=0.6182
[Aug-27-2025_00-24-16] - epoch:115, time: 20.977902(s), valid (NDCG@10: 0.3601, HR@10: 0.6182), test: SKIPPED, all_time: 2702.432314(s)
[Aug-27-2025_00-24-18] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-18] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-21] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-21] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-24] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-24] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-26] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-27] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-27] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-27] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-29] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-29] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-29] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-32] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-32] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-32] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-32] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-35] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-35] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-35] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-35] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-36] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-36] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-36] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-36] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-24-41] - 传统评估结果 - Epoch 116: NDCG@10=0.3642, HR@10=0.6257
[Aug-27-2025_00-24-41] - epoch:116, time: 20.509920(s), valid (NDCG@10: 0.3642, HR@10: 0.6257), test: SKIPPED, all_time: 2722.942234(s)
[Aug-27-2025_00-24-43] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-43] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-46] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-46] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-46] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-46] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-49] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-49] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-49] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-49] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-52] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-52] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-52] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-24-54] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-24-54] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-24-55] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-24-55] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-24-58] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-24-58] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-24-58] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-24-58] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-25-01] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-25-01] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-01] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-01] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-25-02] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-02] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-02] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-02] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-25-07] - 传统评估结果 - Epoch 117: NDCG@10=0.3649, HR@10=0.6204
[Aug-27-2025_00-25-07] - epoch:117, time: 21.323134(s), valid (NDCG@10: 0.3649, HR@10: 0.6204), test: SKIPPED, all_time: 2744.265368(s)
[Aug-27-2025_00-25-09] - 设备类型s聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-25-09] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-25-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-10] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-25-12] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-25-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-12] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-12] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-25-15] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-25-15] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-15] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-15] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-25-18] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-25-18] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-18] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-25-21] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_00-25-21] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-27-2025_00-25-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-21] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-25-24] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-25-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-24] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-24] - 梯度聚类完成: 128 -> 10个客户端
[Aug-27-2025_00-25-26] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_00-25-26] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_00-25-26] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-27] - 梯度聚类完成: 128 -> 11个客户端
[Aug-27-2025_00-25-28] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_00-25-28] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-28] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_00-25-28] - 梯度聚类完成: 47 -> 4个客户端
[Aug-27-2025_00-25-32] - 传统评估结果 - Epoch 118: NDCG@10=0.3519, HR@10=0.6129
[Aug-27-2025_00-25-32] - 早停触发！NDCG在30轮内没有改善。
[Aug-27-2025_00-25-32] - epoch:118, time: 20.814270(s), valid (NDCG@10: 0.3519, HR@10: 0.6129), test: SKIPPED, all_time: 2765.079639(s)
[Aug-27-2025_00-25-32] - [联邦训练] 最佳结果: valid NDCG@10=0.3740, HR@10=0.6299 (测试集评估已跳过)
