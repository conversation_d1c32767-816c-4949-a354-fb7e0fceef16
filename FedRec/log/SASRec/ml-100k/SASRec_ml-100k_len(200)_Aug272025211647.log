{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.3, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Top_k', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_21-16-47] - 算法: base_Top_k
[Aug-27-2025_21-16-47] - 开始训练，配置参数如下：
[Aug-27-2025_21-16-47] - early_stop_enabled: True
[Aug-27-2025_21-16-47] - early_stop: 30
[Aug-27-2025_21-16-47] - eval_k: 10
[Aug-27-2025_21-16-47] - datapath: ../../data/
[Aug-27-2025_21-16-47] - log_path: ../log
[Aug-27-2025_21-16-47] - assign_by_interactions: False
[Aug-27-2025_21-16-47] - device_split: [0.5, 0.3]
[Aug-27-2025_21-16-47] - dim_s: 16
[Aug-27-2025_21-16-47] - dim_m: 32
[Aug-27-2025_21-16-47] - dim_l: 64
[Aug-27-2025_21-16-47] - device_types: {}
[Aug-27-2025_21-16-47] - quantization_bits: 8
[Aug-27-2025_21-16-47] - quantization_method: symmetric
[Aug-27-2025_21-16-47] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_21-16-47] - kmeans_iterations: 10
[Aug-27-2025_21-16-47] - top_k_ratio: 0.3
[Aug-27-2025_21-16-47] - top_k_method: global
[Aug-27-2025_21-16-47] - min_k: 1
[Aug-27-2025_21-16-47] - use_clustering: True
[Aug-27-2025_21-16-47] - cr: 0.9063
[Aug-27-2025_21-16-47] - cluster_range: 0.2
[Aug-27-2025_21-16-47] - model: SASRec
[Aug-27-2025_21-16-47] - algorithm: base_Top_k
[Aug-27-2025_21-16-47] - lr: 0.001
[Aug-27-2025_21-16-47] - batch_size: 256
[Aug-27-2025_21-16-47] - l2_reg: 0
[Aug-27-2025_21-16-47] - l2_emb: 0.0
[Aug-27-2025_21-16-47] - hidden_size: 64
[Aug-27-2025_21-16-47] - dropout: 0.2
[Aug-27-2025_21-16-47] - epochs: 1000000
[Aug-27-2025_21-16-47] - dataset: ml-100k
[Aug-27-2025_21-16-47] - train_data: ml-100k.txt
[Aug-27-2025_21-16-47] - num_layers: 2
[Aug-27-2025_21-16-47] - num_heads: 1
[Aug-27-2025_21-16-47] - inner_size: 256
[Aug-27-2025_21-16-47] - max_seq_len: 200
[Aug-27-2025_21-16-47] - decor_alpha: 0.3
[Aug-27-2025_21-16-47] - neg_num: 99
[Aug-27-2025_21-16-47] - skip_test_eval: True
[Aug-27-2025_21-16-47] - eval_freq: 1
[Aug-27-2025_21-16-47] - full_eval: False
[Aug-27-2025_21-16-47] - c: 9
[Aug-27-2025_21-16-47] - alpha: 0.3
[Aug-27-2025_21-16-47] - kd_ratio: 0.1
[Aug-27-2025_21-16-47] - kd_lr: 0.001
[Aug-27-2025_21-16-47] - distill_epochs: 10
[Aug-27-2025_21-16-47] - distill_freq: 3
[Aug-27-2025_21-16-47] - max_iterations: 1000
[Aug-27-2025_21-16-47] - target_clusters: 105
[Aug-27-2025_21-16-47] - quantize_gradients: True
[Aug-27-2025_21-16-47] - quantization_type: uniform
[Aug-27-2025_21-16-47] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_21-16-47] - 最大序列长度: 200
[Aug-27-2025_21-16-47] - 批次大小: 256
[Aug-27-2025_21-16-48] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_21-16-48] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_21-16-48] - Top-k配置: 比例=0.3, 方法=global, 最小k=1
[Aug-27-2025_21-16-48] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_21-16-48] - 用户数量: 943
[Aug-27-2025_21-16-48] - 物品数量: 1349
[Aug-27-2025_21-16-49] - 服务器初始化完成
[Aug-27-2025_21-16-49] - 初始化通信开销计算器...
[Aug-27-2025_21-16-49] - 预计算模型尺寸 (MB): S=0.1682, M=0.3500, L=0.7605
[Aug-27-2025_21-17-20] - Epoch 1 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-17-24] - 传统评估结果 - Epoch 1: NDCG@10=0.0666, HR@10=0.1357
[Aug-27-2025_21-17-24] - Top-k压缩统计: 总梯度数=16631248, 选择梯度数=16631248, 压缩比例=100.00%
[Aug-27-2025_21-17-24] - epoch:1, time: 31.316524(s), valid (NDCG@10: 0.0666, HR@10: 0.1357), test: SKIPPED, all_time: 31.316524(s)
[Aug-27-2025_21-17-24] - 新的最佳性能: valid NDCG@10=0.0666, valid HR@10=0.1357
