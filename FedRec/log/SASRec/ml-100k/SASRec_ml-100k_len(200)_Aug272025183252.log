{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Q', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_18-32-52] - 算法: base_Q
[Aug-27-2025_18-32-52] - 开始训练，配置参数如下：
[Aug-27-2025_18-32-52] - early_stop_enabled: True
[Aug-27-2025_18-32-52] - early_stop: 30
[Aug-27-2025_18-32-52] - eval_k: 10
[Aug-27-2025_18-32-52] - datapath: ../../data/
[Aug-27-2025_18-32-52] - log_path: ../log
[Aug-27-2025_18-32-52] - assign_by_interactions: False
[Aug-27-2025_18-32-52] - device_split: [0.5, 0.3]
[Aug-27-2025_18-32-52] - dim_s: 16
[Aug-27-2025_18-32-52] - dim_m: 32
[Aug-27-2025_18-32-52] - dim_l: 64
[Aug-27-2025_18-32-52] - device_types: {}
[Aug-27-2025_18-32-52] - quantization_bits: 8
[Aug-27-2025_18-32-52] - quantization_method: symmetric
[Aug-27-2025_18-32-52] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_18-32-52] - kmeans_iterations: 10
[Aug-27-2025_18-32-52] - top_k_ratio: 0.1
[Aug-27-2025_18-32-52] - top_k_method: global
[Aug-27-2025_18-32-52] - min_k: 1
[Aug-27-2025_18-32-52] - use_clustering: True
[Aug-27-2025_18-32-52] - cr: 0.9063
[Aug-27-2025_18-32-52] - cluster_range: 0.2
[Aug-27-2025_18-32-52] - model: SASRec
[Aug-27-2025_18-32-52] - algorithm: base_Q
[Aug-27-2025_18-32-52] - lr: 0.001
[Aug-27-2025_18-32-52] - batch_size: 256
[Aug-27-2025_18-32-52] - l2_reg: 0
[Aug-27-2025_18-32-52] - l2_emb: 0.0
[Aug-27-2025_18-32-52] - hidden_size: 64
[Aug-27-2025_18-32-52] - dropout: 0.2
[Aug-27-2025_18-32-52] - epochs: 1000000
[Aug-27-2025_18-32-52] - dataset: ml-100k
[Aug-27-2025_18-32-52] - train_data: ml-100k.txt
[Aug-27-2025_18-32-52] - num_layers: 2
[Aug-27-2025_18-32-52] - num_heads: 1
[Aug-27-2025_18-32-52] - inner_size: 256
[Aug-27-2025_18-32-52] - max_seq_len: 200
[Aug-27-2025_18-32-52] - decor_alpha: 0.3
[Aug-27-2025_18-32-52] - neg_num: 99
[Aug-27-2025_18-32-52] - skip_test_eval: True
[Aug-27-2025_18-32-52] - eval_freq: 1
[Aug-27-2025_18-32-52] - full_eval: False
[Aug-27-2025_18-32-52] - c: 9
[Aug-27-2025_18-32-52] - alpha: 0.3
[Aug-27-2025_18-32-52] - kd_ratio: 0.1
[Aug-27-2025_18-32-52] - kd_lr: 0.001
[Aug-27-2025_18-32-52] - distill_epochs: 10
[Aug-27-2025_18-32-52] - distill_freq: 3
[Aug-27-2025_18-32-52] - max_iterations: 1000
[Aug-27-2025_18-32-52] - target_clusters: 105
[Aug-27-2025_18-32-52] - quantize_gradients: True
[Aug-27-2025_18-32-52] - quantization_type: uniform
[Aug-27-2025_18-32-52] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_18-32-52] - 最大序列长度: 200
[Aug-27-2025_18-32-52] - 批次大小: 256
[Aug-27-2025_18-32-52] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_18-32-52] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_18-32-52] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_18-32-52] - 启用梯度量化: 8位, 类型: uniform
[Aug-27-2025_18-32-52] - 用户数量: 943
[Aug-27-2025_18-32-52] - 物品数量: 1349
[Aug-27-2025_18-32-54] - 服务器初始化完成
[Aug-27-2025_18-32-54] - 初始化通信开销计算器...
[Aug-27-2025_18-32-54] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-27-2025_18-32-54] - Embedding层尺寸 (MB): S=0.086, M=0.173, L=0.346
[Aug-27-2025_18-32-54] - 启用模型参数量化: 8位
[Aug-27-2025_18-33-31] - COMMUNICATION COST - Epoch 1: Downlink=321.977MB, Uplink=80.494MB, Total=402.471MB
[Aug-27-2025_18-33-31] - 传统评估结果 - Epoch 1: NDCG@10=0.0840, HR@10=0.1644
[Aug-27-2025_18-33-31] - epoch:1, time: 0.001064(s), valid (NDCG@10: 0.0840, HR@10: 0.1644), test: SKIPPED, all_time: 32.368518(s)
[Aug-27-2025_18-33-31] - 新的最佳性能: valid NDCG@10=0.0840, valid HR@10=0.1644
[Aug-27-2025_18-34-12] - COMMUNICATION COST - Epoch 2: Downlink=643.953MB, Uplink=160.988MB, Total=804.942MB
[Aug-27-2025_18-34-12] - 传统评估结果 - Epoch 2: NDCG@10=0.1288, HR@10=0.2545
[Aug-27-2025_18-34-12] - epoch:2, time: 0.000000(s), valid (NDCG@10: 0.1288, HR@10: 0.2545), test: SKIPPED, all_time: 72.315939(s)
[Aug-27-2025_18-34-12] - 新的最佳性能: valid NDCG@10=0.1288, valid HR@10=0.2545
[Aug-27-2025_18-35-04] - COMMUNICATION COST - Epoch 3: Downlink=965.930MB, Uplink=241.483MB, Total=1207.413MB
[Aug-27-2025_18-35-04] - 传统评估结果 - Epoch 3: NDCG@10=0.1566, HR@10=0.3086
[Aug-27-2025_18-35-04] - epoch:3, time: 0.000000(s), valid (NDCG@10: 0.1566, HR@10: 0.3086), test: SKIPPED, all_time: 123.725089(s)
[Aug-27-2025_18-35-04] - 新的最佳性能: valid NDCG@10=0.1566, valid HR@10=0.3086
[Aug-27-2025_18-35-56] - COMMUNICATION COST - Epoch 4: Downlink=1287.907MB, Uplink=321.977MB, Total=1609.884MB
[Aug-27-2025_18-35-56] - 传统评估结果 - Epoch 4: NDCG@10=0.1626, HR@10=0.3266
[Aug-27-2025_18-35-56] - epoch:4, time: 0.000000(s), valid (NDCG@10: 0.1626, HR@10: 0.3266), test: SKIPPED, all_time: 176.369668(s)
[Aug-27-2025_18-35-56] - 新的最佳性能: valid NDCG@10=0.1626, valid HR@10=0.3266
[Aug-27-2025_18-36-42] - COMMUNICATION COST - Epoch 5: Downlink=1609.884MB, Uplink=402.471MB, Total=2012.355MB
[Aug-27-2025_18-36-42] - 传统评估结果 - Epoch 5: NDCG@10=0.1769, HR@10=0.3542
[Aug-27-2025_18-36-42] - epoch:5, time: 0.001409(s), valid (NDCG@10: 0.1769, HR@10: 0.3542), test: SKIPPED, all_time: 222.612984(s)
[Aug-27-2025_18-36-42] - 新的最佳性能: valid NDCG@10=0.1769, valid HR@10=0.3542
[Aug-27-2025_18-37-24] - COMMUNICATION COST - Epoch 6: Downlink=1931.860MB, Uplink=482.965MB, Total=2414.826MB
[Aug-27-2025_18-37-24] - 传统评估结果 - Epoch 6: NDCG@10=0.1810, HR@10=0.3627
[Aug-27-2025_18-37-24] - epoch:6, time: 0.001055(s), valid (NDCG@10: 0.1810, HR@10: 0.3627), test: SKIPPED, all_time: 265.157545(s)
[Aug-27-2025_18-37-24] - 新的最佳性能: valid NDCG@10=0.1810, valid HR@10=0.3627
[Aug-27-2025_18-38-05] - COMMUNICATION COST - Epoch 7: Downlink=2253.837MB, Uplink=563.459MB, Total=2817.297MB
[Aug-27-2025_18-38-05] - 传统评估结果 - Epoch 7: NDCG@10=0.1942, HR@10=0.3786
[Aug-27-2025_18-38-05] - epoch:7, time: 0.000000(s), valid (NDCG@10: 0.1942, HR@10: 0.3786), test: SKIPPED, all_time: 306.128319(s)
[Aug-27-2025_18-38-05] - 新的最佳性能: valid NDCG@10=0.1942, valid HR@10=0.3786
[Aug-27-2025_18-38-46] - COMMUNICATION COST - Epoch 8: Downlink=2575.814MB, Uplink=643.953MB, Total=3219.767MB
[Aug-27-2025_18-38-46] - 传统评估结果 - Epoch 8: NDCG@10=0.1941, HR@10=0.3743
[Aug-27-2025_18-38-46] - epoch:8, time: 0.000000(s), valid (NDCG@10: 0.1941, HR@10: 0.3743), test: SKIPPED, all_time: 347.285911(s)
[Aug-27-2025_18-39-27] - COMMUNICATION COST - Epoch 9: Downlink=2897.791MB, Uplink=724.448MB, Total=3622.238MB
[Aug-27-2025_18-39-27] - 传统评估结果 - Epoch 9: NDCG@10=0.2007, HR@10=0.3743
[Aug-27-2025_18-39-27] - epoch:9, time: 0.000000(s), valid (NDCG@10: 0.2007, HR@10: 0.3743), test: SKIPPED, all_time: 388.698807(s)
