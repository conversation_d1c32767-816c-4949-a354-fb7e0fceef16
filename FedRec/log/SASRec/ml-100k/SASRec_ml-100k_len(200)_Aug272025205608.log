{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Top_k', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_20-56-08] - 算法: base_Top_k
[Aug-27-2025_20-56-08] - 开始训练，配置参数如下：
[Aug-27-2025_20-56-08] - early_stop_enabled: True
[Aug-27-2025_20-56-08] - early_stop: 30
[Aug-27-2025_20-56-08] - eval_k: 10
[Aug-27-2025_20-56-08] - datapath: ../../data/
[Aug-27-2025_20-56-08] - log_path: ../log
[Aug-27-2025_20-56-08] - assign_by_interactions: False
[Aug-27-2025_20-56-08] - device_split: [0.5, 0.3]
[Aug-27-2025_20-56-08] - dim_s: 16
[Aug-27-2025_20-56-08] - dim_m: 32
[Aug-27-2025_20-56-08] - dim_l: 64
[Aug-27-2025_20-56-08] - device_types: {}
[Aug-27-2025_20-56-08] - quantization_bits: 8
[Aug-27-2025_20-56-08] - quantization_method: symmetric
[Aug-27-2025_20-56-08] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_20-56-08] - kmeans_iterations: 10
[Aug-27-2025_20-56-08] - top_k_ratio: 0.1
[Aug-27-2025_20-56-08] - top_k_method: global
[Aug-27-2025_20-56-08] - min_k: 1
[Aug-27-2025_20-56-08] - use_clustering: True
[Aug-27-2025_20-56-08] - cr: 0.9063
[Aug-27-2025_20-56-08] - cluster_range: 0.2
[Aug-27-2025_20-56-08] - model: SASRec
[Aug-27-2025_20-56-08] - algorithm: base_Top_k
[Aug-27-2025_20-56-08] - lr: 0.001
[Aug-27-2025_20-56-08] - batch_size: 256
[Aug-27-2025_20-56-08] - l2_reg: 0
[Aug-27-2025_20-56-08] - l2_emb: 0.0
[Aug-27-2025_20-56-08] - hidden_size: 64
[Aug-27-2025_20-56-08] - dropout: 0.2
[Aug-27-2025_20-56-08] - epochs: 1000000
[Aug-27-2025_20-56-08] - dataset: ml-100k
[Aug-27-2025_20-56-08] - train_data: ml-100k.txt
[Aug-27-2025_20-56-08] - num_layers: 2
[Aug-27-2025_20-56-08] - num_heads: 1
[Aug-27-2025_20-56-08] - inner_size: 256
[Aug-27-2025_20-56-08] - max_seq_len: 200
[Aug-27-2025_20-56-08] - decor_alpha: 0.3
[Aug-27-2025_20-56-08] - neg_num: 99
[Aug-27-2025_20-56-08] - skip_test_eval: True
[Aug-27-2025_20-56-08] - eval_freq: 1
[Aug-27-2025_20-56-08] - full_eval: False
[Aug-27-2025_20-56-08] - c: 9
[Aug-27-2025_20-56-08] - alpha: 0.3
[Aug-27-2025_20-56-08] - kd_ratio: 0.1
[Aug-27-2025_20-56-08] - kd_lr: 0.001
[Aug-27-2025_20-56-08] - distill_epochs: 10
[Aug-27-2025_20-56-08] - distill_freq: 3
[Aug-27-2025_20-56-08] - max_iterations: 1000
[Aug-27-2025_20-56-08] - target_clusters: 105
[Aug-27-2025_20-56-08] - quantize_gradients: True
[Aug-27-2025_20-56-08] - quantization_type: uniform
[Aug-27-2025_20-56-08] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_20-56-08] - 最大序列长度: 200
[Aug-27-2025_20-56-08] - 批次大小: 256
[Aug-27-2025_20-56-09] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_20-56-09] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_20-56-09] - Top-k配置: 比例=0.1, 方法=global, 最小k=1
[Aug-27-2025_20-56-09] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_20-56-09] - 用户数量: 943
[Aug-27-2025_20-56-09] - 物品数量: 1349
[Aug-27-2025_20-56-10] - 服务器初始化完成
[Aug-27-2025_20-56-10] - 初始化通信开销计算器...
[Aug-27-2025_20-56-10] - 预计算模型尺寸 (MB): S=0.1682, M=0.3500, L=0.7605
[Aug-27-2025_20-56-45] - Epoch 1 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_20-56-49] - 传统评估结果 - Epoch 1: NDCG@10=0.0658, HR@10=0.1453
[Aug-27-2025_20-56-49] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_20-56-49] - epoch:1, time: 34.769603(s), valid (NDCG@10: 0.0658, HR@10: 0.1453), test: SKIPPED, all_time: 34.769603(s)
[Aug-27-2025_20-56-49] - 新的最佳性能: valid NDCG@10=0.0658, valid HR@10=0.1453
[Aug-27-2025_20-57-26] - Epoch 2 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_20-57-31] - 传统评估结果 - Epoch 2: NDCG@10=0.0864, HR@10=0.1835
[Aug-27-2025_20-57-31] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_20-57-31] - epoch:2, time: 36.216790(s), valid (NDCG@10: 0.0864, HR@10: 0.1835), test: SKIPPED, all_time: 70.986394(s)
[Aug-27-2025_20-57-31] - 新的最佳性能: valid NDCG@10=0.0864, valid HR@10=0.1835
[Aug-27-2025_20-58-09] - Epoch 3 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_20-58-14] - 传统评估结果 - Epoch 3: NDCG@10=0.0993, HR@10=0.2036
[Aug-27-2025_20-58-14] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_20-58-14] - epoch:3, time: 37.393614(s), valid (NDCG@10: 0.0993, HR@10: 0.2036), test: SKIPPED, all_time: 108.380008(s)
[Aug-27-2025_20-58-14] - 新的最佳性能: valid NDCG@10=0.0993, valid HR@10=0.2036
[Aug-27-2025_20-58-51] - Epoch 4 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_20-58-56] - 传统评估结果 - Epoch 4: NDCG@10=0.1224, HR@10=0.2407
[Aug-27-2025_20-58-56] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_20-58-56] - epoch:4, time: 37.056275(s), valid (NDCG@10: 0.1224, HR@10: 0.2407), test: SKIPPED, all_time: 145.436283(s)
[Aug-27-2025_20-58-56] - 新的最佳性能: valid NDCG@10=0.1224, valid HR@10=0.2407
[Aug-27-2025_20-59-34] - Epoch 5 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_20-59-40] - 传统评估结果 - Epoch 5: NDCG@10=0.1356, HR@10=0.2768
[Aug-27-2025_20-59-40] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_20-59-40] - epoch:5, time: 37.799186(s), valid (NDCG@10: 0.1356, HR@10: 0.2768), test: SKIPPED, all_time: 183.235469(s)
[Aug-27-2025_20-59-40] - 新的最佳性能: valid NDCG@10=0.1356, valid HR@10=0.2768
[Aug-27-2025_21-00-19] - Epoch 6 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-00-24] - 传统评估结果 - Epoch 6: NDCG@10=0.1508, HR@10=0.2906
[Aug-27-2025_21-00-24] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-00-24] - epoch:6, time: 39.538695(s), valid (NDCG@10: 0.1508, HR@10: 0.2906), test: SKIPPED, all_time: 222.774163(s)
[Aug-27-2025_21-00-24] - 新的最佳性能: valid NDCG@10=0.1508, valid HR@10=0.2906
[Aug-27-2025_21-01-01] - Epoch 7 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-01-06] - 传统评估结果 - Epoch 7: NDCG@10=0.1624, HR@10=0.3266
[Aug-27-2025_21-01-06] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-01-06] - epoch:7, time: 36.811223(s), valid (NDCG@10: 0.1624, HR@10: 0.3266), test: SKIPPED, all_time: 259.585386(s)
[Aug-27-2025_21-01-06] - 新的最佳性能: valid NDCG@10=0.1624, valid HR@10=0.3266
[Aug-27-2025_21-01-44] - Epoch 8 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-01-49] - 传统评估结果 - Epoch 8: NDCG@10=0.1766, HR@10=0.3436
[Aug-27-2025_21-01-49] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-01-49] - epoch:8, time: 37.772295(s), valid (NDCG@10: 0.1766, HR@10: 0.3436), test: SKIPPED, all_time: 297.357681(s)
[Aug-27-2025_21-01-49] - 新的最佳性能: valid NDCG@10=0.1766, valid HR@10=0.3436
[Aug-27-2025_21-02-26] - Epoch 9 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-02-32] - 传统评估结果 - Epoch 9: NDCG@10=0.1854, HR@10=0.3701
[Aug-27-2025_21-02-32] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-02-32] - epoch:9, time: 36.875546(s), valid (NDCG@10: 0.1854, HR@10: 0.3701), test: SKIPPED, all_time: 334.233227(s)
[Aug-27-2025_21-02-32] - 新的最佳性能: valid NDCG@10=0.1854, valid HR@10=0.3701
[Aug-27-2025_21-03-06] - Epoch 10 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-03-10] - 传统评估结果 - Epoch 10: NDCG@10=0.1899, HR@10=0.3712
[Aug-27-2025_21-03-10] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-03-10] - epoch:10, time: 33.670443(s), valid (NDCG@10: 0.1899, HR@10: 0.3712), test: SKIPPED, all_time: 367.903670(s)
[Aug-27-2025_21-03-10] - 新的最佳性能: valid NDCG@10=0.1899, valid HR@10=0.3712
[Aug-27-2025_21-03-43] - Epoch 11 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-03-47] - 传统评估结果 - Epoch 11: NDCG@10=0.2059, HR@10=0.3934
[Aug-27-2025_21-03-47] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-03-47] - epoch:11, time: 32.572362(s), valid (NDCG@10: 0.2059, HR@10: 0.3934), test: SKIPPED, all_time: 400.476032(s)
[Aug-27-2025_21-03-47] - 新的最佳性能: valid NDCG@10=0.2059, valid HR@10=0.3934
[Aug-27-2025_21-04-21] - Epoch 12 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-04-25] - 传统评估结果 - Epoch 12: NDCG@10=0.2164, HR@10=0.3998
[Aug-27-2025_21-04-25] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-04-25] - epoch:12, time: 33.801288(s), valid (NDCG@10: 0.2164, HR@10: 0.3998), test: SKIPPED, all_time: 434.277320(s)
[Aug-27-2025_21-04-25] - 新的最佳性能: valid NDCG@10=0.2164, valid HR@10=0.3998
[Aug-27-2025_21-04-58] - Epoch 13 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-05-01] - 传统评估结果 - Epoch 13: NDCG@10=0.2213, HR@10=0.4115
[Aug-27-2025_21-05-01] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-05-01] - epoch:13, time: 32.937469(s), valid (NDCG@10: 0.2213, HR@10: 0.4115), test: SKIPPED, all_time: 467.214789(s)
[Aug-27-2025_21-05-01] - 新的最佳性能: valid NDCG@10=0.2213, valid HR@10=0.4115
[Aug-27-2025_21-05-33] - Epoch 14 通信开销: 上行 = 321.9767 MB, 下行 = 321.9767 MB, 总计 = 643.9535 MB
[Aug-27-2025_21-05-37] - 传统评估结果 - Epoch 14: NDCG@10=0.2304, HR@10=0.4263
[Aug-27-2025_21-05-37] - Top-k压缩统计: 总梯度数=15495136, 选择梯度数=15495136, 压缩比例=100.00%
[Aug-27-2025_21-05-37] - epoch:14, time: 31.991186(s), valid (NDCG@10: 0.2304, HR@10: 0.4263), test: SKIPPED, all_time: 499.205975(s)
[Aug-27-2025_21-05-37] - 新的最佳性能: valid NDCG@10=0.2304, valid HR@10=0.4263
