{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Q', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_20-25-24] - 算法: base_Q
[Aug-27-2025_20-25-24] - 开始训练，配置参数如下：
[Aug-27-2025_20-25-24] - early_stop_enabled: True
[Aug-27-2025_20-25-24] - early_stop: 30
[Aug-27-2025_20-25-24] - eval_k: 10
[Aug-27-2025_20-25-24] - datapath: ../../data/
[Aug-27-2025_20-25-24] - log_path: ../log
[Aug-27-2025_20-25-24] - assign_by_interactions: False
[Aug-27-2025_20-25-24] - device_split: [0.5, 0.3]
[Aug-27-2025_20-25-24] - dim_s: 16
[Aug-27-2025_20-25-24] - dim_m: 32
[Aug-27-2025_20-25-24] - dim_l: 64
[Aug-27-2025_20-25-24] - device_types: {}
[Aug-27-2025_20-25-24] - quantization_bits: 8
[Aug-27-2025_20-25-24] - quantization_method: symmetric
[Aug-27-2025_20-25-24] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_20-25-24] - kmeans_iterations: 10
[Aug-27-2025_20-25-24] - top_k_ratio: 0.1
[Aug-27-2025_20-25-24] - top_k_method: global
[Aug-27-2025_20-25-24] - min_k: 1
[Aug-27-2025_20-25-24] - use_clustering: True
[Aug-27-2025_20-25-24] - cr: 0.9063
[Aug-27-2025_20-25-24] - cluster_range: 0.2
[Aug-27-2025_20-25-24] - model: SASRec
[Aug-27-2025_20-25-24] - algorithm: base_Q
[Aug-27-2025_20-25-24] - lr: 0.001
[Aug-27-2025_20-25-24] - batch_size: 256
[Aug-27-2025_20-25-24] - l2_reg: 0
[Aug-27-2025_20-25-24] - l2_emb: 0.0
[Aug-27-2025_20-25-24] - hidden_size: 64
[Aug-27-2025_20-25-24] - dropout: 0.2
[Aug-27-2025_20-25-24] - epochs: 1000000
[Aug-27-2025_20-25-24] - dataset: ml-100k
[Aug-27-2025_20-25-24] - train_data: ml-100k.txt
[Aug-27-2025_20-25-24] - num_layers: 2
[Aug-27-2025_20-25-24] - num_heads: 1
[Aug-27-2025_20-25-24] - inner_size: 256
[Aug-27-2025_20-25-24] - max_seq_len: 200
[Aug-27-2025_20-25-24] - decor_alpha: 0.3
[Aug-27-2025_20-25-24] - neg_num: 99
[Aug-27-2025_20-25-24] - skip_test_eval: True
[Aug-27-2025_20-25-24] - eval_freq: 1
[Aug-27-2025_20-25-24] - full_eval: False
[Aug-27-2025_20-25-24] - c: 9
[Aug-27-2025_20-25-24] - alpha: 0.3
[Aug-27-2025_20-25-24] - kd_ratio: 0.1
[Aug-27-2025_20-25-24] - kd_lr: 0.001
[Aug-27-2025_20-25-24] - distill_epochs: 10
[Aug-27-2025_20-25-24] - distill_freq: 3
[Aug-27-2025_20-25-24] - max_iterations: 1000
[Aug-27-2025_20-25-24] - target_clusters: 105
[Aug-27-2025_20-25-24] - quantize_gradients: True
[Aug-27-2025_20-25-24] - quantization_type: uniform
[Aug-27-2025_20-25-24] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_20-25-24] - 最大序列长度: 200
[Aug-27-2025_20-25-24] - 批次大小: 256
[Aug-27-2025_20-25-24] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_20-25-24] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_20-25-24] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_20-25-24] - 启用梯度量化: 8位, 类型: uniform
[Aug-27-2025_20-25-24] - 用户数量: 943
[Aug-27-2025_20-25-24] - 物品数量: 1349
[Aug-27-2025_20-25-25] - 服务器初始化完成
[Aug-27-2025_20-25-25] - 初始化通信开销计算器...
[Aug-27-2025_20-25-25] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-27-2025_20-25-25] - 模型张量数: S=36, M=36, L=36
[Aug-27-2025_20-25-25] - 启用模型参数量化: 8位
[Aug-27-2025_20-25-56] - COMM COST - Epoch 1: Down=80.624MB, Up=80.624MB, Total=161.247MB
[Aug-27-2025_20-25-56] - EVAL - Epoch 1: NDCG@10=0.0810, HR@10=0.1782
[Aug-27-2025_20-25-56] - epoch:1, time: 26.995227(s), valid (NDCG@10: 0.0810, HR@10: 0.1782), test: SKIPPED, all_time: 26.995227(s)
[Aug-27-2025_20-26-29] - COMM COST - Epoch 2: Down=161.247MB, Up=161.247MB, Total=322.495MB
[Aug-27-2025_20-26-29] - EVAL - Epoch 2: NDCG@10=0.1234, HR@10=0.2577
[Aug-27-2025_20-26-29] - epoch:2, time: 28.285305(s), valid (NDCG@10: 0.1234, HR@10: 0.2577), test: SKIPPED, all_time: 55.280532(s)
[Aug-27-2025_20-27-03] - COMM COST - Epoch 3: Down=241.871MB, Up=241.871MB, Total=483.742MB
[Aug-27-2025_20-27-03] - EVAL - Epoch 3: NDCG@10=0.1492, HR@10=0.3012
[Aug-27-2025_20-27-03] - epoch:3, time: 29.168691(s), valid (NDCG@10: 0.1492, HR@10: 0.3012), test: SKIPPED, all_time: 84.449222(s)
[Aug-27-2025_20-27-39] - COMM COST - Epoch 4: Down=322.495MB, Up=322.495MB, Total=644.990MB
[Aug-27-2025_20-27-39] - EVAL - Epoch 4: NDCG@10=0.1623, HR@10=0.3234
[Aug-27-2025_20-27-39] - epoch:4, time: 30.856407(s), valid (NDCG@10: 0.1623, HR@10: 0.3234), test: SKIPPED, all_time: 115.305629(s)
[Aug-27-2025_20-28-12] - COMM COST - Epoch 5: Down=403.118MB, Up=403.118MB, Total=806.237MB
[Aug-27-2025_20-28-12] - EVAL - Epoch 5: NDCG@10=0.1690, HR@10=0.3425
[Aug-27-2025_20-28-12] - epoch:5, time: 28.870960(s), valid (NDCG@10: 0.1690, HR@10: 0.3425), test: SKIPPED, all_time: 144.176589(s)
[Aug-27-2025_20-28-47] - COMM COST - Epoch 6: Down=483.742MB, Up=483.742MB, Total=967.484MB
[Aug-27-2025_20-28-47] - EVAL - Epoch 6: NDCG@10=0.1731, HR@10=0.3563
[Aug-27-2025_20-28-47] - epoch:6, time: 30.158037(s), valid (NDCG@10: 0.1731, HR@10: 0.3563), test: SKIPPED, all_time: 174.334626(s)
[Aug-27-2025_20-29-21] - COMM COST - Epoch 7: Down=564.366MB, Up=564.366MB, Total=1128.732MB
[Aug-27-2025_20-29-21] - EVAL - Epoch 7: NDCG@10=0.1744, HR@10=0.3446
[Aug-27-2025_20-29-21] - epoch:7, time: 29.891681(s), valid (NDCG@10: 0.1744, HR@10: 0.3446), test: SKIPPED, all_time: 204.226306(s)
[Aug-27-2025_20-29-57] - COMM COST - Epoch 8: Down=644.990MB, Up=644.990MB, Total=1289.979MB
[Aug-27-2025_20-29-57] - EVAL - Epoch 8: NDCG@10=0.1790, HR@10=0.3468
[Aug-27-2025_20-29-57] - epoch:8, time: 31.627177(s), valid (NDCG@10: 0.1790, HR@10: 0.3468), test: SKIPPED, all_time: 235.853484(s)
