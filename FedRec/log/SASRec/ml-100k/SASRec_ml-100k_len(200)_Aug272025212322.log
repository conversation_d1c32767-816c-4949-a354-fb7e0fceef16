{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.3, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Top_k', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_21-23-22] - 算法: base_Top_k
[Aug-27-2025_21-23-22] - 开始训练，配置参数如下：
[Aug-27-2025_21-23-22] - early_stop_enabled: True
[Aug-27-2025_21-23-22] - early_stop: 30
[Aug-27-2025_21-23-22] - eval_k: 10
[Aug-27-2025_21-23-22] - datapath: ../../data/
[Aug-27-2025_21-23-22] - log_path: ../log
[Aug-27-2025_21-23-22] - assign_by_interactions: False
[Aug-27-2025_21-23-22] - device_split: [0.5, 0.3]
[Aug-27-2025_21-23-22] - dim_s: 16
[Aug-27-2025_21-23-22] - dim_m: 32
[Aug-27-2025_21-23-22] - dim_l: 64
[Aug-27-2025_21-23-22] - device_types: {}
[Aug-27-2025_21-23-22] - quantization_bits: 8
[Aug-27-2025_21-23-22] - quantization_method: symmetric
[Aug-27-2025_21-23-22] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_21-23-22] - kmeans_iterations: 10
[Aug-27-2025_21-23-22] - top_k_ratio: 0.3
[Aug-27-2025_21-23-22] - top_k_method: global
[Aug-27-2025_21-23-22] - min_k: 1
[Aug-27-2025_21-23-22] - use_clustering: True
[Aug-27-2025_21-23-22] - cr: 0.9063
[Aug-27-2025_21-23-22] - cluster_range: 0.2
[Aug-27-2025_21-23-22] - model: SASRec
[Aug-27-2025_21-23-22] - algorithm: base_Top_k
[Aug-27-2025_21-23-22] - lr: 0.001
[Aug-27-2025_21-23-22] - batch_size: 256
[Aug-27-2025_21-23-22] - l2_reg: 0
[Aug-27-2025_21-23-22] - l2_emb: 0.0
[Aug-27-2025_21-23-22] - hidden_size: 64
[Aug-27-2025_21-23-22] - dropout: 0.2
[Aug-27-2025_21-23-22] - epochs: 1000000
[Aug-27-2025_21-23-22] - dataset: ml-100k
[Aug-27-2025_21-23-22] - train_data: ml-100k.txt
[Aug-27-2025_21-23-22] - num_layers: 2
[Aug-27-2025_21-23-22] - num_heads: 1
[Aug-27-2025_21-23-22] - inner_size: 256
[Aug-27-2025_21-23-22] - max_seq_len: 200
[Aug-27-2025_21-23-22] - decor_alpha: 0.3
[Aug-27-2025_21-23-22] - neg_num: 99
[Aug-27-2025_21-23-22] - skip_test_eval: True
[Aug-27-2025_21-23-22] - eval_freq: 1
[Aug-27-2025_21-23-22] - full_eval: False
[Aug-27-2025_21-23-22] - c: 9
[Aug-27-2025_21-23-22] - alpha: 0.3
[Aug-27-2025_21-23-22] - kd_ratio: 0.1
[Aug-27-2025_21-23-22] - kd_lr: 0.001
[Aug-27-2025_21-23-22] - distill_epochs: 10
[Aug-27-2025_21-23-22] - distill_freq: 3
[Aug-27-2025_21-23-22] - max_iterations: 1000
[Aug-27-2025_21-23-22] - target_clusters: 105
[Aug-27-2025_21-23-22] - quantize_gradients: True
[Aug-27-2025_21-23-22] - quantization_type: uniform
[Aug-27-2025_21-23-22] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_21-23-22] - 最大序列长度: 200
[Aug-27-2025_21-23-22] - 批次大小: 256
[Aug-27-2025_21-23-23] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_21-23-23] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_21-23-23] - Top-k配置: 比例=0.3, 方法=global, 最小k=1
[Aug-27-2025_21-23-23] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_21-23-23] - 用户数量: 943
[Aug-27-2025_21-23-23] - 物品数量: 1349
[Aug-27-2025_21-23-25] - 服务器初始化完成
[Aug-27-2025_21-23-25] - 初始化通信开销计算器...
[Aug-27-2025_21-23-25] - 预计算模型尺寸 (MB): S=0.1682, M=0.3500, L=0.7605
