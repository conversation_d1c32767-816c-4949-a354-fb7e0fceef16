{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Q', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_20-10-54] - 算法: base_Q
[Aug-27-2025_20-10-54] - 开始训练，配置参数如下：
[Aug-27-2025_20-10-54] - early_stop_enabled: True
[Aug-27-2025_20-10-54] - early_stop: 30
[Aug-27-2025_20-10-54] - eval_k: 10
[Aug-27-2025_20-10-54] - datapath: ../../data/
[Aug-27-2025_20-10-54] - log_path: ../log
[Aug-27-2025_20-10-54] - assign_by_interactions: False
[Aug-27-2025_20-10-54] - device_split: [0.5, 0.3]
[Aug-27-2025_20-10-54] - dim_s: 16
[Aug-27-2025_20-10-54] - dim_m: 32
[Aug-27-2025_20-10-54] - dim_l: 64
[Aug-27-2025_20-10-54] - device_types: {}
[Aug-27-2025_20-10-54] - quantization_bits: 8
[Aug-27-2025_20-10-54] - quantization_method: symmetric
[Aug-27-2025_20-10-54] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_20-10-54] - kmeans_iterations: 10
[Aug-27-2025_20-10-54] - top_k_ratio: 0.1
[Aug-27-2025_20-10-54] - top_k_method: global
[Aug-27-2025_20-10-54] - min_k: 1
[Aug-27-2025_20-10-54] - use_clustering: True
[Aug-27-2025_20-10-54] - cr: 0.9063
[Aug-27-2025_20-10-54] - cluster_range: 0.2
[Aug-27-2025_20-10-54] - model: SASRec
[Aug-27-2025_20-10-54] - algorithm: base_Q
[Aug-27-2025_20-10-54] - lr: 0.001
[Aug-27-2025_20-10-54] - batch_size: 256
[Aug-27-2025_20-10-54] - l2_reg: 0
[Aug-27-2025_20-10-54] - l2_emb: 0.0
[Aug-27-2025_20-10-54] - hidden_size: 64
[Aug-27-2025_20-10-54] - dropout: 0.2
[Aug-27-2025_20-10-54] - epochs: 1000000
[Aug-27-2025_20-10-54] - dataset: ml-100k
[Aug-27-2025_20-10-54] - train_data: ml-100k.txt
[Aug-27-2025_20-10-54] - num_layers: 2
[Aug-27-2025_20-10-54] - num_heads: 1
[Aug-27-2025_20-10-54] - inner_size: 256
[Aug-27-2025_20-10-54] - max_seq_len: 200
[Aug-27-2025_20-10-54] - decor_alpha: 0.3
[Aug-27-2025_20-10-54] - neg_num: 99
[Aug-27-2025_20-10-54] - skip_test_eval: True
[Aug-27-2025_20-10-54] - eval_freq: 1
[Aug-27-2025_20-10-54] - full_eval: False
[Aug-27-2025_20-10-54] - c: 9
[Aug-27-2025_20-10-54] - alpha: 0.3
[Aug-27-2025_20-10-54] - kd_ratio: 0.1
[Aug-27-2025_20-10-54] - kd_lr: 0.001
[Aug-27-2025_20-10-54] - distill_epochs: 10
[Aug-27-2025_20-10-54] - distill_freq: 3
[Aug-27-2025_20-10-54] - max_iterations: 1000
[Aug-27-2025_20-10-54] - target_clusters: 105
[Aug-27-2025_20-10-54] - quantize_gradients: True
[Aug-27-2025_20-10-54] - quantization_type: uniform
[Aug-27-2025_20-10-54] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_20-10-54] - 最大序列长度: 200
[Aug-27-2025_20-10-54] - 批次大小: 256
[Aug-27-2025_20-10-54] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_20-10-54] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_20-10-54] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_20-10-54] - 启用梯度量化: 8位, 类型: uniform
[Aug-27-2025_20-10-54] - 用户数量: 943
[Aug-27-2025_20-10-54] - 物品数量: 1349
[Aug-27-2025_20-10-55] - 服务器初始化完成
[Aug-27-2025_20-10-55] - 启用模型参数量化: 8位
[Aug-27-2025_20-11-25] - Epoch 1 通信开销: 上行 = 321.98 KB, 下行 = 717.15 KB, 总计 = 1039.13 KB
[Aug-27-2025_20-11-29] - 传统评估结果 - Epoch 1: NDCG@10=0.0563, HR@10=0.1315
[Aug-27-2025_20-11-29] - epoch:1, time: 0.000000(s), valid (NDCG@10: 0.0563, HR@10: 0.1315), test: SKIPPED, all_time: 29.278296(s)
[Aug-27-2025_20-11-29] - 新的最佳性能: valid NDCG@10=0.0563, valid HR@10=0.1315
[Aug-27-2025_20-11-58] - Epoch 2 通信开销: 上行 = 321.98 KB, 下行 = 717.15 KB, 总计 = 1039.13 KB
[Aug-27-2025_20-12-02] - 传统评估结果 - Epoch 2: NDCG@10=0.1037, HR@10=0.2153
[Aug-27-2025_20-12-02] - epoch:2, time: 0.001003(s), valid (NDCG@10: 0.1037, HR@10: 0.2153), test: SKIPPED, all_time: 62.468948(s)
[Aug-27-2025_20-12-02] - 新的最佳性能: valid NDCG@10=0.1037, valid HR@10=0.2153
[Aug-27-2025_20-12-32] - Epoch 3 通信开销: 上行 = 321.98 KB, 下行 = 717.15 KB, 总计 = 1039.13 KB
[Aug-27-2025_20-12-38] - 传统评估结果 - Epoch 3: NDCG@10=0.1319, HR@10=0.2757
[Aug-27-2025_20-12-38] - epoch:3, time: 0.000000(s), valid (NDCG@10: 0.1319, HR@10: 0.2757), test: SKIPPED, all_time: 96.705692(s)
[Aug-27-2025_20-12-38] - 新的最佳性能: valid NDCG@10=0.1319, valid HR@10=0.2757
