{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Top_k', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_20-52-06] - 算法: base_Top_k
[Aug-27-2025_20-52-06] - 开始训练，配置参数如下：
[Aug-27-2025_20-52-06] - early_stop_enabled: True
[Aug-27-2025_20-52-06] - early_stop: 30
[Aug-27-2025_20-52-06] - eval_k: 10
[Aug-27-2025_20-52-06] - datapath: ../../data/
[Aug-27-2025_20-52-06] - log_path: ../log
[Aug-27-2025_20-52-06] - assign_by_interactions: False
[Aug-27-2025_20-52-06] - device_split: [0.5, 0.3]
[Aug-27-2025_20-52-06] - dim_s: 16
[Aug-27-2025_20-52-06] - dim_m: 32
[Aug-27-2025_20-52-06] - dim_l: 64
[Aug-27-2025_20-52-06] - device_types: {}
[Aug-27-2025_20-52-06] - quantization_bits: 8
[Aug-27-2025_20-52-06] - quantization_method: symmetric
[Aug-27-2025_20-52-06] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_20-52-06] - kmeans_iterations: 10
[Aug-27-2025_20-52-06] - top_k_ratio: 0.1
[Aug-27-2025_20-52-06] - top_k_method: global
[Aug-27-2025_20-52-06] - min_k: 1
[Aug-27-2025_20-52-06] - use_clustering: True
[Aug-27-2025_20-52-06] - cr: 0.9063
[Aug-27-2025_20-52-06] - cluster_range: 0.2
[Aug-27-2025_20-52-06] - model: SASRec
[Aug-27-2025_20-52-06] - algorithm: base_Top_k
[Aug-27-2025_20-52-06] - lr: 0.001
[Aug-27-2025_20-52-06] - batch_size: 256
[Aug-27-2025_20-52-06] - l2_reg: 0
[Aug-27-2025_20-52-06] - l2_emb: 0.0
[Aug-27-2025_20-52-06] - hidden_size: 64
[Aug-27-2025_20-52-06] - dropout: 0.2
[Aug-27-2025_20-52-06] - epochs: 1000000
[Aug-27-2025_20-52-06] - dataset: ml-100k
[Aug-27-2025_20-52-06] - train_data: ml-100k.txt
[Aug-27-2025_20-52-06] - num_layers: 2
[Aug-27-2025_20-52-06] - num_heads: 1
[Aug-27-2025_20-52-06] - inner_size: 256
[Aug-27-2025_20-52-06] - max_seq_len: 200
[Aug-27-2025_20-52-06] - decor_alpha: 0.3
[Aug-27-2025_20-52-06] - neg_num: 99
[Aug-27-2025_20-52-06] - skip_test_eval: True
[Aug-27-2025_20-52-06] - eval_freq: 1
[Aug-27-2025_20-52-06] - full_eval: False
[Aug-27-2025_20-52-06] - c: 9
[Aug-27-2025_20-52-06] - alpha: 0.3
[Aug-27-2025_20-52-06] - kd_ratio: 0.1
[Aug-27-2025_20-52-06] - kd_lr: 0.001
[Aug-27-2025_20-52-06] - distill_epochs: 10
[Aug-27-2025_20-52-06] - distill_freq: 3
[Aug-27-2025_20-52-06] - max_iterations: 1000
[Aug-27-2025_20-52-06] - target_clusters: 105
[Aug-27-2025_20-52-06] - quantize_gradients: True
[Aug-27-2025_20-52-06] - quantization_type: uniform
[Aug-27-2025_20-52-06] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_20-52-06] - 最大序列长度: 200
[Aug-27-2025_20-52-06] - 批次大小: 256
[Aug-27-2025_20-52-06] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_20-52-06] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_20-52-06] - Top-k配置: 比例=0.1, 方法=global, 最小k=1
[Aug-27-2025_20-52-06] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_20-52-06] - 用户数量: 943
[Aug-27-2025_20-52-06] - 物品数量: 1349
[Aug-27-2025_20-52-07] - 服务器初始化完成
[Aug-27-2025_20-52-07] - 初始化通信开销计算器...
[Aug-27-2025_20-52-07] - 预计算模型尺寸 (MB): S=0.1682, M=0.3500, L=0.7605
[Aug-27-2025_20-52-45] - Epoch 1 通信开销: 上行 = 965.9302 MB, 下行 = 321.9767 MB, 总计 = 1287.9070 MB
[Aug-27-2025_20-52-51] - 传统评估结果 - Epoch 1: NDCG@10=0.0447, HR@10=0.0944
[Aug-27-2025_20-52-51] - Top-k压缩统计: 总梯度数=15076960, 选择梯度数=15076960, 压缩比例=100.00%
[Aug-27-2025_20-52-51] - epoch:1, time: 37.809363(s), valid (NDCG@10: 0.0447, HR@10: 0.0944), test: SKIPPED, all_time: 37.809363(s)
[Aug-27-2025_20-52-51] - 新的最佳性能: valid NDCG@10=0.0447, valid HR@10=0.0944
[Aug-27-2025_20-53-27] - Epoch 2 通信开销: 上行 = 965.9302 MB, 下行 = 321.9767 MB, 总计 = 1287.9070 MB
[Aug-27-2025_20-53-31] - 传统评估结果 - Epoch 2: NDCG@10=0.0599, HR@10=0.1315
[Aug-27-2025_20-53-31] - Top-k压缩统计: 总梯度数=15076960, 选择梯度数=15076960, 压缩比例=100.00%
[Aug-27-2025_20-53-31] - epoch:2, time: 36.742539(s), valid (NDCG@10: 0.0599, HR@10: 0.1315), test: SKIPPED, all_time: 74.551902(s)
[Aug-27-2025_20-53-31] - 新的最佳性能: valid NDCG@10=0.0599, valid HR@10=0.1315
[Aug-27-2025_20-54-03] - Epoch 3 通信开销: 上行 = 965.9302 MB, 下行 = 321.9767 MB, 总计 = 1287.9070 MB
[Aug-27-2025_20-54-08] - 传统评估结果 - Epoch 3: NDCG@10=0.0843, HR@10=0.1771
[Aug-27-2025_20-54-08] - Top-k压缩统计: 总梯度数=15076960, 选择梯度数=15076960, 压缩比例=100.00%
[Aug-27-2025_20-54-08] - epoch:3, time: 32.016488(s), valid (NDCG@10: 0.0843, HR@10: 0.1771), test: SKIPPED, all_time: 106.568390(s)
[Aug-27-2025_20-54-08] - 新的最佳性能: valid NDCG@10=0.0843, valid HR@10=0.1771
[Aug-27-2025_20-54-41] - Epoch 4 通信开销: 上行 = 965.9302 MB, 下行 = 321.9767 MB, 总计 = 1287.9070 MB
[Aug-27-2025_20-54-45] - 传统评估结果 - Epoch 4: NDCG@10=0.1049, HR@10=0.2131
[Aug-27-2025_20-54-45] - Top-k压缩统计: 总梯度数=15076960, 选择梯度数=15076960, 压缩比例=100.00%
[Aug-27-2025_20-54-45] - epoch:4, time: 33.043382(s), valid (NDCG@10: 0.1049, HR@10: 0.2131), test: SKIPPED, all_time: 139.611773(s)
[Aug-27-2025_20-54-45] - 新的最佳性能: valid NDCG@10=0.1049, valid HR@10=0.2131
