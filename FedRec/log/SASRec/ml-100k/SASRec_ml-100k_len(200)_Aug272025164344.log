{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_16-43-44] - 算法: base_DHC
[Aug-27-2025_16-43-44] - 开始训练，配置参数如下：
[Aug-27-2025_16-43-44] - early_stop_enabled: True
[Aug-27-2025_16-43-44] - early_stop: 30
[Aug-27-2025_16-43-44] - eval_k: 10
[Aug-27-2025_16-43-44] - datapath: ../../data/
[Aug-27-2025_16-43-44] - log_path: ../log
[Aug-27-2025_16-43-44] - assign_by_interactions: False
[Aug-27-2025_16-43-44] - device_split: [0.5, 0.3]
[Aug-27-2025_16-43-44] - dim_s: 16
[Aug-27-2025_16-43-44] - dim_m: 32
[Aug-27-2025_16-43-44] - dim_l: 64
[Aug-27-2025_16-43-44] - device_types: {}
[Aug-27-2025_16-43-44] - quantization_bits: 8
[Aug-27-2025_16-43-44] - quantization_method: symmetric
[Aug-27-2025_16-43-44] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_16-43-44] - kmeans_iterations: 10
[Aug-27-2025_16-43-44] - top_k_ratio: 0.1
[Aug-27-2025_16-43-44] - top_k_method: global
[Aug-27-2025_16-43-44] - min_k: 1
[Aug-27-2025_16-43-44] - use_clustering: True
[Aug-27-2025_16-43-44] - cr: 0.9063
[Aug-27-2025_16-43-44] - cluster_range: 0.2
[Aug-27-2025_16-43-44] - model: SASRec
[Aug-27-2025_16-43-44] - algorithm: base_DHC
[Aug-27-2025_16-43-44] - lr: 0.001
[Aug-27-2025_16-43-44] - batch_size: 128
[Aug-27-2025_16-43-44] - l2_reg: 0
[Aug-27-2025_16-43-44] - l2_emb: 0.0
[Aug-27-2025_16-43-44] - hidden_size: 64
[Aug-27-2025_16-43-44] - dropout: 0.2
[Aug-27-2025_16-43-44] - epochs: 1000000
[Aug-27-2025_16-43-44] - dataset: ml-100k
[Aug-27-2025_16-43-44] - train_data: ml-100k.txt
[Aug-27-2025_16-43-44] - num_layers: 2
[Aug-27-2025_16-43-44] - num_heads: 1
[Aug-27-2025_16-43-44] - inner_size: 256
[Aug-27-2025_16-43-44] - max_seq_len: 200
[Aug-27-2025_16-43-44] - decor_alpha: 0.3
[Aug-27-2025_16-43-44] - neg_num: 99
[Aug-27-2025_16-43-44] - skip_test_eval: True
[Aug-27-2025_16-43-44] - eval_freq: 1
[Aug-27-2025_16-43-44] - full_eval: False
[Aug-27-2025_16-43-44] - c: 9
[Aug-27-2025_16-43-44] - alpha: 0.3
[Aug-27-2025_16-43-44] - kd_ratio: 0.1
[Aug-27-2025_16-43-44] - kd_lr: 0.001
[Aug-27-2025_16-43-44] - distill_epochs: 10
[Aug-27-2025_16-43-44] - distill_freq: 3
[Aug-27-2025_16-43-44] - max_iterations: 1000
[Aug-27-2025_16-43-44] - target_clusters: 105
[Aug-27-2025_16-43-44] - quantize_gradients: True
[Aug-27-2025_16-43-44] - quantization_type: uniform
[Aug-27-2025_16-43-44] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_16-43-44] - 最大序列长度: 200
[Aug-27-2025_16-43-44] - 批次大小: 128
[Aug-27-2025_16-43-46] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_16-43-46] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_16-43-46] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_16-43-46] - 用户数量: 943
[Aug-27-2025_16-43-46] - 物品数量: 1349
[Aug-27-2025_16-43-47] - 梯度聚类功能已启用: 目标聚类数(C_e)=126, 初始聚类数(C_i)=100, 聚类上限(C_m)=151
[Aug-27-2025_16-43-47] - 服务器初始化完成
[Aug-27-2025_16-43-47] - 初始化通信开销计算器...
[Aug-27-2025_16-43-47] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-27-2025_16-43-47] - Embedding层尺寸 (MB): S=0.086, M=0.173, L=0.346
[Aug-27-2025_16-43-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-43-53] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-43-53] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-43-53] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-43-56] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-43-56] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-43-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-43-56] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-43-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-43-59] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-43-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-43-59] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-44-03] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-04] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-04] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-04] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-44-07] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-07] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-07] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-07] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-44-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-10] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-10] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-10] - 代表性客户端数量: S=4, M=2, L=1
[Aug-27-2025_16-44-12] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-12] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-12] - 设备类型l聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-12] - 代表性客户端数量: S=4, M=2, L=2
[Aug-27-2025_16-44-14] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-14] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_16-44-14] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_16-44-14] - 代表性客户端数量: S=1, M=1, L=1
[Aug-27-2025_16-44-18] - COMMUNICATION COST - Epoch 1: Downlink=717.150MB, Uplink=182.066MB, Total=899.216MB
[Aug-27-2025_16-44-18] - 传统评估结果 - Epoch 1: NDCG@10=0.0775, HR@10=0.1654
[Aug-27-2025_16-44-18] - epoch:1, time: 26.425670(s), valid (NDCG@10: 0.0775, HR@10: 0.1654), test: SKIPPED, all_time: 26.425670(s)
[Aug-27-2025_16-44-18] - 新的最佳性能: valid NDCG@10=0.0775, valid HR@10=0.1654
[Aug-27-2025_16-44-20] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-20] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-20] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-20] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-23] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-23] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-23] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-23] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-44-25] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-25] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-25] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-28] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-28] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-28] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-28] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-31] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-31] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-31] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-31] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-33] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-33] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-34] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-34] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-44-36] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-36] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-36] - 设备类型l聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-37] - 代表性客户端数量: S=5, M=3, L=3
[Aug-27-2025_16-44-38] - 设备类型s聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-38] - 设备类型m聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_16-44-38] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-27-2025_16-44-38] - 代表性客户端数量: S=2, M=1, L=1
[Aug-27-2025_16-44-42] - COMMUNICATION COST - Epoch 2: Downlink=1434.299MB, Uplink=368.581MB, Total=1802.880MB
[Aug-27-2025_16-44-42] - 传统评估结果 - Epoch 2: NDCG@10=0.1371, HR@10=0.2672
[Aug-27-2025_16-44-42] - epoch:2, time: 20.030594(s), valid (NDCG@10: 0.1371, HR@10: 0.2672), test: SKIPPED, all_time: 46.456264(s)
[Aug-27-2025_16-44-42] - 新的最佳性能: valid NDCG@10=0.1371, valid HR@10=0.2672
[Aug-27-2025_16-44-45] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-45] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-45] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-45] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-44-48] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-48] - 代表性客户端数量: S=5, M=3, L=2
[Aug-27-2025_16-44-51] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-51] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-51] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-51] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-54] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-54] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-54] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-54] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-44-57] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-27-2025_16-44-57] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-44-57] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-44-57] - 代表性客户端数量: S=6, M=3, L=2
[Aug-27-2025_16-45-00] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-27-2025_16-45-00] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-27-2025_16-45-00] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-27-2025_16-45-00] - 代表性客户端数量: S=5, M=3, L=2
