{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_Q', 'lr': 0.001, 'batch_size': 256, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-27-2025_18-39-52] - 算法: base_Q
[Aug-27-2025_18-39-52] - 开始训练，配置参数如下：
[Aug-27-2025_18-39-52] - early_stop_enabled: True
[Aug-27-2025_18-39-52] - early_stop: 30
[Aug-27-2025_18-39-52] - eval_k: 10
[Aug-27-2025_18-39-52] - datapath: ../../data/
[Aug-27-2025_18-39-52] - log_path: ../log
[Aug-27-2025_18-39-52] - assign_by_interactions: False
[Aug-27-2025_18-39-52] - device_split: [0.5, 0.3]
[Aug-27-2025_18-39-52] - dim_s: 16
[Aug-27-2025_18-39-52] - dim_m: 32
[Aug-27-2025_18-39-52] - dim_l: 64
[Aug-27-2025_18-39-52] - device_types: {}
[Aug-27-2025_18-39-52] - quantization_bits: 8
[Aug-27-2025_18-39-52] - quantization_method: symmetric
[Aug-27-2025_18-39-52] - adaptive_percentile: [0.01, 0.99]
[Aug-27-2025_18-39-52] - kmeans_iterations: 10
[Aug-27-2025_18-39-52] - top_k_ratio: 0.1
[Aug-27-2025_18-39-52] - top_k_method: global
[Aug-27-2025_18-39-52] - min_k: 1
[Aug-27-2025_18-39-52] - use_clustering: True
[Aug-27-2025_18-39-52] - cr: 0.9063
[Aug-27-2025_18-39-52] - cluster_range: 0.2
[Aug-27-2025_18-39-52] - model: SASRec
[Aug-27-2025_18-39-52] - algorithm: base_Q
[Aug-27-2025_18-39-52] - lr: 0.001
[Aug-27-2025_18-39-52] - batch_size: 256
[Aug-27-2025_18-39-52] - l2_reg: 0
[Aug-27-2025_18-39-52] - l2_emb: 0.0
[Aug-27-2025_18-39-52] - hidden_size: 64
[Aug-27-2025_18-39-52] - dropout: 0.2
[Aug-27-2025_18-39-52] - epochs: 1000000
[Aug-27-2025_18-39-52] - dataset: ml-100k
[Aug-27-2025_18-39-52] - train_data: ml-100k.txt
[Aug-27-2025_18-39-52] - num_layers: 2
[Aug-27-2025_18-39-52] - num_heads: 1
[Aug-27-2025_18-39-52] - inner_size: 256
[Aug-27-2025_18-39-52] - max_seq_len: 200
[Aug-27-2025_18-39-52] - decor_alpha: 0.3
[Aug-27-2025_18-39-52] - neg_num: 99
[Aug-27-2025_18-39-52] - skip_test_eval: True
[Aug-27-2025_18-39-52] - eval_freq: 1
[Aug-27-2025_18-39-52] - full_eval: False
[Aug-27-2025_18-39-52] - c: 9
[Aug-27-2025_18-39-52] - alpha: 0.3
[Aug-27-2025_18-39-52] - kd_ratio: 0.1
[Aug-27-2025_18-39-52] - kd_lr: 0.001
[Aug-27-2025_18-39-52] - distill_epochs: 10
[Aug-27-2025_18-39-52] - distill_freq: 3
[Aug-27-2025_18-39-52] - max_iterations: 1000
[Aug-27-2025_18-39-52] - target_clusters: 105
[Aug-27-2025_18-39-52] - quantize_gradients: True
[Aug-27-2025_18-39-52] - quantization_type: uniform
[Aug-27-2025_18-39-52] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-27-2025_18-39-52] - 最大序列长度: 200
[Aug-27-2025_18-39-52] - 批次大小: 256
[Aug-27-2025_18-39-53] - 嵌套设备类型分配完成(随机分配): 小型=471(49.9%), 中型=283(30.0%), 大型=189(20.0%)
[Aug-27-2025_18-39-53] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-27-2025_18-39-53] - 设备类型分布: 小型:471, 中型:283, 大型:189
[Aug-27-2025_18-39-53] - 启用梯度量化: 8位, 类型: uniform
[Aug-27-2025_18-39-53] - 用户数量: 943
[Aug-27-2025_18-39-53] - 物品数量: 1349
[Aug-27-2025_18-39-55] - 服务器初始化完成
[Aug-27-2025_18-39-55] - 初始化通信开销计算器 (高级版)...
[Aug-27-2025_18-39-55] - 模型总尺寸 (MB): S=0.176, M=0.367, L=0.797
[Aug-27-2025_18-39-55] - 模型张量数: S=36, M=36, L=36
[Aug-27-2025_18-39-55] - 启用模型参数量化: 8位
[Aug-27-2025_18-40-46] - COMMUNICATION COST - Epoch 1: Downlink=80.624MB, Uplink=80.624MB, Total=161.247MB
[Aug-27-2025_18-40-46] - 传统评估结果 - Epoch 1: NDCG@10=0.0639, HR@10=0.1379
[Aug-27-2025_18-40-46] - epoch:1, time: 0.000000(s), valid (NDCG@10: 0.0639, HR@10: 0.1379), test: SKIPPED, all_time: 45.187017(s)
[Aug-27-2025_18-40-46] - 新的最佳性能: valid NDCG@10=0.0639, valid HR@10=0.1379
[Aug-27-2025_18-41-29] - COMMUNICATION COST - Epoch 2: Downlink=161.247MB, Uplink=161.247MB, Total=322.495MB
[Aug-27-2025_18-41-29] - 传统评估结果 - Epoch 2: NDCG@10=0.1187, HR@10=0.2450
[Aug-27-2025_18-41-29] - epoch:2, time: 0.000000(s), valid (NDCG@10: 0.1187, HR@10: 0.2450), test: SKIPPED, all_time: 89.616722(s)
[Aug-27-2025_18-41-29] - 新的最佳性能: valid NDCG@10=0.1187, valid HR@10=0.2450
[Aug-27-2025_18-42-07] - COMMUNICATION COST - Epoch 3: Downlink=241.871MB, Uplink=241.871MB, Total=483.742MB
[Aug-27-2025_18-42-07] - 传统评估结果 - Epoch 3: NDCG@10=0.1402, HR@10=0.2906
[Aug-27-2025_18-42-07] - epoch:3, time: 0.000000(s), valid (NDCG@10: 0.1402, HR@10: 0.2906), test: SKIPPED, all_time: 127.431330(s)
[Aug-27-2025_18-42-07] - 新的最佳性能: valid NDCG@10=0.1402, valid HR@10=0.2906
[Aug-27-2025_18-42-40] - COMMUNICATION COST - Epoch 4: Downlink=322.495MB, Uplink=322.495MB, Total=644.990MB
[Aug-27-2025_18-42-40] - 传统评估结果 - Epoch 4: NDCG@10=0.1580, HR@10=0.3118
[Aug-27-2025_18-42-40] - epoch:4, time: 0.000000(s), valid (NDCG@10: 0.1580, HR@10: 0.3118), test: SKIPPED, all_time: 160.900252(s)
[Aug-27-2025_18-42-40] - 新的最佳性能: valid NDCG@10=0.1580, valid HR@10=0.3118
[Aug-27-2025_18-43-14] - COMMUNICATION COST - Epoch 5: Downlink=403.118MB, Uplink=403.118MB, Total=806.237MB
[Aug-27-2025_18-43-14] - 传统评估结果 - Epoch 5: NDCG@10=0.1613, HR@10=0.3256
[Aug-27-2025_18-43-14] - epoch:5, time: 0.000000(s), valid (NDCG@10: 0.1613, HR@10: 0.3256), test: SKIPPED, all_time: 194.297590(s)
[Aug-27-2025_18-43-14] - 新的最佳性能: valid NDCG@10=0.1613, valid HR@10=0.3256
[Aug-27-2025_18-43-50] - COMMUNICATION COST - Epoch 6: Downlink=483.742MB, Uplink=483.742MB, Total=967.484MB
[Aug-27-2025_18-43-50] - 传统评估结果 - Epoch 6: NDCG@10=0.1775, HR@10=0.3531
[Aug-27-2025_18-43-50] - epoch:6, time: 0.000000(s), valid (NDCG@10: 0.1775, HR@10: 0.3531), test: SKIPPED, all_time: 230.199792(s)
[Aug-27-2025_18-43-50] - 新的最佳性能: valid NDCG@10=0.1775, valid HR@10=0.3531
[Aug-27-2025_18-44-27] - COMMUNICATION COST - Epoch 7: Downlink=564.366MB, Uplink=564.366MB, Total=1128.732MB
[Aug-27-2025_18-44-27] - 传统评估结果 - Epoch 7: NDCG@10=0.1823, HR@10=0.3701
[Aug-27-2025_18-44-27] - epoch:7, time: 0.000000(s), valid (NDCG@10: 0.1823, HR@10: 0.3701), test: SKIPPED, all_time: 267.468263(s)
[Aug-27-2025_18-44-27] - 新的最佳性能: valid NDCG@10=0.1823, valid HR@10=0.3701
[Aug-27-2025_18-44-59] - COMMUNICATION COST - Epoch 8: Downlink=644.990MB, Uplink=644.990MB, Total=1289.979MB
[Aug-27-2025_18-44-59] - 传统评估结果 - Epoch 8: NDCG@10=0.1788, HR@10=0.3574
[Aug-27-2025_18-44-59] - epoch:8, time: 0.000000(s), valid (NDCG@10: 0.1788, HR@10: 0.3574), test: SKIPPED, all_time: 299.433240(s)
