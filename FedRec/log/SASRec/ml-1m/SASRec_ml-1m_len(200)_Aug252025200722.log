{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cluster_threshold': 0.8, 'target_clusters': 105, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'cluster_range': 0.2, 'cr': 0.9063, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-25-2025_20-07-22] - 算法: base_DHC
[Aug-25-2025_20-07-22] - 开始训练，配置参数如下：
[Aug-25-2025_20-07-22] - early_stop_enabled: True
[Aug-25-2025_20-07-22] - early_stop: 30
[Aug-25-2025_20-07-22] - eval_k: 10
[Aug-25-2025_20-07-22] - datapath: ../../data/
[Aug-25-2025_20-07-22] - log_path: ../log
[Aug-25-2025_20-07-22] - assign_by_interactions: False
[Aug-25-2025_20-07-22] - device_split: [0.5, 0.3]
[Aug-25-2025_20-07-22] - dim_s: 16
[Aug-25-2025_20-07-22] - dim_m: 32
[Aug-25-2025_20-07-22] - dim_l: 64
[Aug-25-2025_20-07-22] - device_types: {}
[Aug-25-2025_20-07-22] - quantization_bits: 8
[Aug-25-2025_20-07-22] - quantization_method: symmetric
[Aug-25-2025_20-07-22] - adaptive_percentile: [0.01, 0.99]
[Aug-25-2025_20-07-22] - kmeans_iterations: 10
[Aug-25-2025_20-07-22] - top_k_ratio: 0.1
[Aug-25-2025_20-07-22] - top_k_method: global
[Aug-25-2025_20-07-22] - min_k: 1
[Aug-25-2025_20-07-22] - use_clustering: True
[Aug-25-2025_20-07-22] - cluster_threshold: 0.8
[Aug-25-2025_20-07-22] - target_clusters: 105
[Aug-25-2025_20-07-22] - model: SASRec
[Aug-25-2025_20-07-22] - algorithm: base_DHC
[Aug-25-2025_20-07-22] - lr: 0.001
[Aug-25-2025_20-07-22] - batch_size: 128
[Aug-25-2025_20-07-22] - l2_reg: 0
[Aug-25-2025_20-07-22] - l2_emb: 0.0
[Aug-25-2025_20-07-22] - hidden_size: 64
[Aug-25-2025_20-07-22] - dropout: 0.2
[Aug-25-2025_20-07-22] - epochs: 1000000
[Aug-25-2025_20-07-22] - dataset: ml-1m
[Aug-25-2025_20-07-22] - train_data: ml-1m.txt
[Aug-25-2025_20-07-22] - num_layers: 2
[Aug-25-2025_20-07-22] - num_heads: 1
[Aug-25-2025_20-07-22] - inner_size: 256
[Aug-25-2025_20-07-22] - max_seq_len: 200
[Aug-25-2025_20-07-22] - decor_alpha: 0.3
[Aug-25-2025_20-07-22] - neg_num: 99
[Aug-25-2025_20-07-22] - skip_test_eval: True
[Aug-25-2025_20-07-22] - eval_freq: 1
[Aug-25-2025_20-07-22] - full_eval: False
[Aug-25-2025_20-07-22] - c: 9
[Aug-25-2025_20-07-22] - alpha: 0.3
[Aug-25-2025_20-07-22] - kd_ratio: 0.1
[Aug-25-2025_20-07-22] - kd_lr: 0.001
[Aug-25-2025_20-07-22] - distill_epochs: 10
[Aug-25-2025_20-07-22] - distill_freq: 3
[Aug-25-2025_20-07-22] - max_iterations: 1000
[Aug-25-2025_20-07-22] - cluster_range: 0.2
[Aug-25-2025_20-07-22] - cr: 0.9063
[Aug-25-2025_20-07-22] - quantize_gradients: True
[Aug-25-2025_20-07-22] - quantization_type: uniform
[Aug-25-2025_20-07-22] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-25-2025_20-07-22] - 最大序列长度: 200
[Aug-25-2025_20-07-22] - 批次大小: 128
[Aug-25-2025_20-07-24] - 嵌套设备类型分配完成(随机分配): 小型=3020(50.0%), 中型=1812(30.0%), 大型=1208(20.0%)
[Aug-25-2025_20-07-24] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-25-2025_20-07-24] - 设备类型分布: 小型:3020, 中型:1812, 大型:1208
[Aug-25-2025_20-07-24] - 用户数量: 6040
[Aug-25-2025_20-07-24] - 物品数量: 3416
[Aug-25-2025_20-07-27] - 梯度聚类功能已启用: 目标聚类数(C_e)=320, 初始聚类数(C_i)=256, 聚类上限(C_m)=384
[Aug-25-2025_20-07-27] - 服务器初始化完成
[Aug-25-2025_20-07-40] - 设备类型s的梯度聚类完成: 65 -> 1个簇
