{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-26-2025_22-53-07] - 算法: base_DHC
[Aug-26-2025_22-53-07] - 开始训练，配置参数如下：
[Aug-26-2025_22-53-07] - early_stop_enabled: True
[Aug-26-2025_22-53-07] - early_stop: 30
[Aug-26-2025_22-53-07] - eval_k: 10
[Aug-26-2025_22-53-07] - datapath: ../../data/
[Aug-26-2025_22-53-07] - log_path: ../log
[Aug-26-2025_22-53-07] - assign_by_interactions: False
[Aug-26-2025_22-53-07] - device_split: [0.5, 0.3]
[Aug-26-2025_22-53-07] - dim_s: 16
[Aug-26-2025_22-53-07] - dim_m: 32
[Aug-26-2025_22-53-07] - dim_l: 64
[Aug-26-2025_22-53-07] - device_types: {}
[Aug-26-2025_22-53-07] - quantization_bits: 8
[Aug-26-2025_22-53-07] - quantization_method: symmetric
[Aug-26-2025_22-53-07] - adaptive_percentile: [0.01, 0.99]
[Aug-26-2025_22-53-07] - kmeans_iterations: 10
[Aug-26-2025_22-53-07] - top_k_ratio: 0.1
[Aug-26-2025_22-53-07] - top_k_method: global
[Aug-26-2025_22-53-07] - min_k: 1
[Aug-26-2025_22-53-07] - use_clustering: True
[Aug-26-2025_22-53-07] - cr: 0.9063
[Aug-26-2025_22-53-07] - cluster_range: 0.2
[Aug-26-2025_22-53-07] - model: SASRec
[Aug-26-2025_22-53-07] - algorithm: base_DHC
[Aug-26-2025_22-53-07] - lr: 0.001
[Aug-26-2025_22-53-07] - batch_size: 128
[Aug-26-2025_22-53-07] - l2_reg: 0
[Aug-26-2025_22-53-07] - l2_emb: 0.0
[Aug-26-2025_22-53-07] - hidden_size: 64
[Aug-26-2025_22-53-07] - dropout: 0.2
[Aug-26-2025_22-53-07] - epochs: 1000000
[Aug-26-2025_22-53-07] - dataset: ml-1m
[Aug-26-2025_22-53-07] - train_data: ml-1m.txt
[Aug-26-2025_22-53-07] - num_layers: 2
[Aug-26-2025_22-53-07] - num_heads: 1
[Aug-26-2025_22-53-07] - inner_size: 256
[Aug-26-2025_22-53-07] - max_seq_len: 200
[Aug-26-2025_22-53-07] - decor_alpha: 0.3
[Aug-26-2025_22-53-07] - neg_num: 99
[Aug-26-2025_22-53-07] - skip_test_eval: True
[Aug-26-2025_22-53-07] - eval_freq: 1
[Aug-26-2025_22-53-07] - full_eval: False
[Aug-26-2025_22-53-07] - c: 9
[Aug-26-2025_22-53-07] - alpha: 0.3
[Aug-26-2025_22-53-07] - kd_ratio: 0.1
[Aug-26-2025_22-53-07] - kd_lr: 0.001
[Aug-26-2025_22-53-07] - distill_epochs: 10
[Aug-26-2025_22-53-07] - distill_freq: 3
[Aug-26-2025_22-53-07] - max_iterations: 1000
[Aug-26-2025_22-53-07] - target_clusters: 105
[Aug-26-2025_22-53-07] - quantize_gradients: True
[Aug-26-2025_22-53-07] - quantization_type: uniform
[Aug-26-2025_22-53-07] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-26-2025_22-53-07] - 最大序列长度: 200
[Aug-26-2025_22-53-07] - 批次大小: 128
[Aug-26-2025_22-53-11] - 嵌套设备类型分配完成(随机分配): 小型=3020(50.0%), 中型=1812(30.0%), 大型=1208(20.0%)
[Aug-26-2025_22-53-12] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-26-2025_22-53-12] - 设备类型分布: 小型:3020, 中型:1812, 大型:1208
[Aug-26-2025_22-53-12] - 用户数量: 6040
[Aug-26-2025_22-53-12] - 物品数量: 3416
[Aug-26-2025_22-53-13] - 梯度聚类功能已启用: 目标聚类数(C_e)=320, 初始聚类数(C_i)=256, 聚类上限(C_m)=384
[Aug-26-2025_22-53-13] - 服务器初始化完成
[Aug-26-2025_22-53-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-53-19] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_22-53-19] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-53-19] - 梯度聚类完成: 128 -> 7个客户端
