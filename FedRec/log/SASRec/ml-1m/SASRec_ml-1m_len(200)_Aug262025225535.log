{'early_stop_enabled': True, 'early_stop': 30, 'eval_k': 10, 'datapath': '../../data/', 'log_path': '../log', 'assign_by_interactions': False, 'device_split': [0.5, 0.3], 'dim_s': 16, 'dim_m': 32, 'dim_l': 64, 'device_types': {}, 'quantization_bits': 8, 'quantization_method': 'symmetric', 'adaptive_percentile': [0.01, 0.99], 'kmeans_iterations': 10, 'top_k_ratio': 0.1, 'top_k_method': 'global', 'min_k': 1, 'use_clustering': True, 'cr': 0.9063, 'cluster_range': 0.2, 'model': 'SASRec', 'algorithm': 'base_DHC', 'lr': 0.001, 'batch_size': 128, 'l2_reg': 0, 'l2_emb': 0.0, 'hidden_size': 64, 'dropout': 0.2, 'epochs': 1000000, 'dataset': 'ml-1m', 'train_data': 'ml-1m.txt', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'decor_alpha': 0.3, 'neg_num': 99, 'skip_test_eval': True, 'eval_freq': 1, 'full_eval': False, 'c': 9, 'alpha': 0.3, 'kd_ratio': 0.1, 'kd_lr': 0.001, 'distill_epochs': 10, 'distill_freq': 3, 'max_iterations': 1000, 'target_clusters': 105, 'quantize_gradients': True, 'quantization_type': 'uniform'}
[Aug-26-2025_22-55-35] - 算法: base_DHC
[Aug-26-2025_22-55-35] - 开始训练，配置参数如下：
[Aug-26-2025_22-55-35] - early_stop_enabled: True
[Aug-26-2025_22-55-35] - early_stop: 30
[Aug-26-2025_22-55-35] - eval_k: 10
[Aug-26-2025_22-55-35] - datapath: ../../data/
[Aug-26-2025_22-55-35] - log_path: ../log
[Aug-26-2025_22-55-35] - assign_by_interactions: False
[Aug-26-2025_22-55-35] - device_split: [0.5, 0.3]
[Aug-26-2025_22-55-35] - dim_s: 16
[Aug-26-2025_22-55-35] - dim_m: 32
[Aug-26-2025_22-55-35] - dim_l: 64
[Aug-26-2025_22-55-35] - device_types: {}
[Aug-26-2025_22-55-35] - quantization_bits: 8
[Aug-26-2025_22-55-35] - quantization_method: symmetric
[Aug-26-2025_22-55-35] - adaptive_percentile: [0.01, 0.99]
[Aug-26-2025_22-55-35] - kmeans_iterations: 10
[Aug-26-2025_22-55-35] - top_k_ratio: 0.1
[Aug-26-2025_22-55-35] - top_k_method: global
[Aug-26-2025_22-55-35] - min_k: 1
[Aug-26-2025_22-55-35] - use_clustering: True
[Aug-26-2025_22-55-35] - cr: 0.9063
[Aug-26-2025_22-55-35] - cluster_range: 0.2
[Aug-26-2025_22-55-35] - model: SASRec
[Aug-26-2025_22-55-35] - algorithm: base_DHC
[Aug-26-2025_22-55-35] - lr: 0.001
[Aug-26-2025_22-55-35] - batch_size: 128
[Aug-26-2025_22-55-35] - l2_reg: 0
[Aug-26-2025_22-55-35] - l2_emb: 0.0
[Aug-26-2025_22-55-35] - hidden_size: 64
[Aug-26-2025_22-55-35] - dropout: 0.2
[Aug-26-2025_22-55-35] - epochs: 1000000
[Aug-26-2025_22-55-35] - dataset: ml-1m
[Aug-26-2025_22-55-35] - train_data: ml-1m.txt
[Aug-26-2025_22-55-35] - num_layers: 2
[Aug-26-2025_22-55-35] - num_heads: 1
[Aug-26-2025_22-55-35] - inner_size: 256
[Aug-26-2025_22-55-35] - max_seq_len: 200
[Aug-26-2025_22-55-35] - decor_alpha: 0.3
[Aug-26-2025_22-55-35] - neg_num: 99
[Aug-26-2025_22-55-35] - skip_test_eval: True
[Aug-26-2025_22-55-35] - eval_freq: 1
[Aug-26-2025_22-55-35] - full_eval: False
[Aug-26-2025_22-55-35] - c: 9
[Aug-26-2025_22-55-35] - alpha: 0.3
[Aug-26-2025_22-55-35] - kd_ratio: 0.1
[Aug-26-2025_22-55-35] - kd_lr: 0.001
[Aug-26-2025_22-55-35] - distill_epochs: 10
[Aug-26-2025_22-55-35] - distill_freq: 3
[Aug-26-2025_22-55-35] - max_iterations: 1000
[Aug-26-2025_22-55-35] - target_clusters: 105
[Aug-26-2025_22-55-35] - quantize_gradients: True
[Aug-26-2025_22-55-35] - quantization_type: uniform
[Aug-26-2025_22-55-35] - 训练数据: ../../data/ml-1m/ml-1m.txt
[Aug-26-2025_22-55-35] - 最大序列长度: 200
[Aug-26-2025_22-55-35] - 批次大小: 128
[Aug-26-2025_22-55-37] - 嵌套设备类型分配完成(随机分配): 小型=3020(50.0%), 中型=1812(30.0%), 大型=1208(20.0%)
[Aug-26-2025_22-55-37] - 异构设备配置: dim_s=16, dim_m=32, dim_l=64
[Aug-26-2025_22-55-37] - 设备类型分布: 小型:3020, 中型:1812, 大型:1208
[Aug-26-2025_22-55-37] - 用户数量: 6040
[Aug-26-2025_22-55-37] - 物品数量: 3416
[Aug-26-2025_22-55-38] - 梯度聚类功能已启用: 目标聚类数(C_e)=320, 初始聚类数(C_i)=256, 聚类上限(C_m)=384
[Aug-26-2025_22-55-38] - 服务器初始化完成
[Aug-26-2025_22-55-41] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-55-43] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-55-43] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-55-43] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-55-48] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-55-48] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-55-48] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-55-48] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-55-52] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-55-52] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-55-52] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-55-53] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-55-56] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-55-56] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-55-56] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-55-56] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-55-59] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-55-59] - 设备类型m聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-55-59] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-00] - 梯度聚类完成: 128 -> 6个客户端
[Aug-26-2025_22-56-02] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-56-02] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-56-03] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-03] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-56-06] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-56-06] - 设备类型m聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-06] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-06] - 梯度聚类完成: 128 -> 6个客户端
[Aug-26-2025_22-56-10] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-56-10] - 设备类型m聚类参数: 目标聚类数=4, 初始聚类数=3, 聚类上限=4
[Aug-26-2025_22-56-10] - 设备类型l聚类参数: 目标聚类数=1, 初始聚类数=1, 聚类上限=1
[Aug-26-2025_22-56-10] - 梯度聚类完成: 128 -> 8个客户端
[Aug-26-2025_22-56-14] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-56-14] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_22-56-14] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-14] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_22-56-17] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-56-18] - 设备类型m聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-18] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-18] - 梯度聚类完成: 128 -> 6个客户端
[Aug-26-2025_22-56-21] - 设备类型s聚类参数: 目标聚类数=6, 初始聚类数=4, 聚类上限=7
[Aug-26-2025_22-56-21] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_22-56-21] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-21] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_22-56-24] - 设备类型s聚类参数: 目标聚类数=5, 初始聚类数=4, 聚类上限=6
[Aug-26-2025_22-56-24] - 设备类型m聚类参数: 目标聚类数=3, 初始聚类数=2, 聚类上限=3
[Aug-26-2025_22-56-25] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-25] - 梯度聚类完成: 128 -> 7个客户端
[Aug-26-2025_22-56-29] - 设备类型s聚类参数: 目标聚类数=7, 初始聚类数=5, 聚类上限=8
[Aug-26-2025_22-56-29] - 设备类型m聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-29] - 设备类型l聚类参数: 目标聚类数=2, 初始聚类数=1, 聚类上限=2
[Aug-26-2025_22-56-29] - 梯度聚类完成: 128 -> 7个客户端
